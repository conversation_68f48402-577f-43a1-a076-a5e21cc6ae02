#!/usr/bin/env python3
"""
测试使用配置中的域名生成邮箱功能
"""

import json
import os
from app.utils import generate_random_string

def test_config_domain_generation():
    """测试使用配置中的域名生成邮箱"""
    print("🧪 测试使用配置中的域名生成邮箱功能...")
    
    config_file = "tempmail_config.json"
    
    # 检查配置文件
    if not os.path.exists(config_file):
        print("❌ 配置文件不存在")
        return
    
    # 读取配置
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print(f"\n📋 当前配置内容:")
    mailboxes = config.get('mailboxes', [])
    print(f"  - 邮箱数量: {len(mailboxes)}")
    print(f"  - 上次使用: {config.get('last_email', '无')}")
    
    # 提取配置中的域名
    domains = set()
    for mailbox in mailboxes:
        domain = mailbox.get('domain', '')
        if domain:
            domains.add(domain)
            email = mailbox.get('email', '')
            pin = mailbox.get('pin', '')
            print(f"  - 邮箱: {email} (域名: {domain}, PIN: {pin or '无'})")
    
    if not domains:
        print("❌ 配置中没有找到域名")
        return
    
    print(f"\n🎯 提取到的域名 ({len(domains)} 个):")
    for i, domain in enumerate(sorted(domains), 1):
        print(f"  {i}. {domain}")
    
    # 使用每个域名生成邮箱
    print(f"\n🎲 使用配置域名生成邮箱:")
    generated_emails = []
    
    for domain in sorted(domains):
        # 生成随机用户名
        username = generate_random_string(8, include_numbers=True, include_uppercase=False)
        new_email = f"{username}@{domain}"
        generated_emails.append({
            'email': new_email,
            'domain': domain,
            'username': username
        })
        
        print(f"  📧 域名: {domain} -> 邮箱: {new_email}")
    
    # 测试保存新生成的邮箱
    print(f"\n💾 测试保存新生成的邮箱:")
    
    # 备份原配置
    backup_file = "tempmail_config_backup_domain.json"
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    print(f"  ✅ 已备份原配置到 {backup_file}")
    
    # 添加生成的邮箱到配置
    original_count = len(config.get('mailboxes', []))
    existing_emails = [mb.get('email') for mb in config.get('mailboxes', [])]
    
    added_count = 0
    for email_info in generated_emails:
        email = email_info['email']
        domain = email_info['domain']
        
        if email not in existing_emails:
            new_mailbox = {
                'email': email,
                'pin': '',
                'domain': domain
            }
            config['mailboxes'].append(new_mailbox)
            added_count += 1
            print(f"    ➕ 添加: {email} (域名: {domain})")
        else:
            print(f"    ⚠️  已存在: {email}")
    
    # 设置最后一个生成的邮箱为当前邮箱
    if generated_emails:
        config['last_email'] = generated_emails[-1]['email']
    
    # 保存配置
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 保存结果:")
    print(f"  - 原有邮箱: {original_count} 个")
    print(f"  - 新增邮箱: {added_count} 个")
    print(f"  - 总计邮箱: {len(config['mailboxes'])} 个")
    print(f"  - 当前邮箱: {config.get('last_email', '无')}")
    
    # 验证保存结果
    print(f"\n✅ 验证保存结果:")
    with open(config_file, 'r', encoding='utf-8') as f:
        saved_config = json.load(f)
    
    print(f"  📋 配置文件中的所有邮箱:")
    for i, mailbox in enumerate(saved_config.get('mailboxes', []), 1):
        email = mailbox.get('email', '未知')
        domain = mailbox.get('domain', '未知')
        pin = mailbox.get('pin', '')
        print(f"    {i}. {email} (域名: {domain}, PIN: {pin or '无'})")
    
    # 按域名分组显示
    print(f"\n📊 按域名分组:")
    domain_groups = {}
    for mailbox in saved_config.get('mailboxes', []):
        domain = mailbox.get('domain', '未知')
        email = mailbox.get('email', '未知')
        if domain not in domain_groups:
            domain_groups[domain] = []
        domain_groups[domain].append(email)
    
    for domain, emails in domain_groups.items():
        print(f"  🏷️  域名: {domain} ({len(emails)} 个邮箱)")
        for email in emails:
            print(f"      - {email}")
    
    # 询问是否恢复原配置
    print(f"\n🔄 测试完成！")
    restore = input("是否恢复原配置？(y/N): ").strip().lower()
    if restore == 'y':
        with open(backup_file, 'r', encoding='utf-8') as f:
            original_config = json.load(f)
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(original_config, f, ensure_ascii=False, indent=2)
        print("✅ 已恢复原配置")
        os.remove(backup_file)
        print("🗑️  已删除备份文件")
    else:
        print("📝 保留测试结果，备份文件已保存")

if __name__ == "__main__":
    test_config_domain_generation()
