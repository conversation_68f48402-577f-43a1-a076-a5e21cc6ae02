"""
工具函数模块
包含各种通用的工具函数
"""

import logging
from datetime import datetime, timezone, timedelta
import re

logger = logging.getLogger(__name__)


def _is_valid_6digit_code(code: str) -> bool:
    """
    检查是否是有效的6位数字验证码（内部函数）

    Args:
        code: 要检查的验证码字符串

    Returns:
        bool: 如果是有效的6位数字验证码返回True，否则返回False
    """
    if not code or len(code) != 6:
        return False

    # 必须是纯数字
    if not code.isdigit():
        return False

    # 排除明显不是验证码的6位数字
    invalid_codes = {
        '000000', '111111', '222222', '333333', '444444',
        '555555', '666666', '777777', '888888', '999999',  # 重复数字
        '123456', '654321', '012345', '543210',  # 连续数字
        '111222', '112233', '223344', '334455',  # 重复模式
    }

    if code in invalid_codes:
        return False

    # 排除日期格式（如：202401, 240101等）
    if re.match(r'^20\d{4}$', code) or re.match(r'^\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])$', code):
        return False

    return True


def convert_utc_to_beijing(utc_time_str: str) -> str:
    """
    将UTC时间转换为北京时间的工具函数
    
    Args:
        utc_time_str: UTC时间字符串，支持多种格式：
                     - RFC 2822: Mon, 28 Jul 2025 01:53:12 +0000
                     - ISO: 2025-07-28T01:53:12Z
                     - 简单格式: 2025-07-28 01:53:12
    
    Returns:
        str: 转换后的北京时间字符串，格式为 "YYYY-MM-DD HH:MM:SS (北京时间)"
    """
    try:
        if not utc_time_str or utc_time_str == '未知':
            return utc_time_str
        
        # 解析不同格式的时间字符串
        utc_dt = None
        
        # 尝试解析 RFC 2822 格式: Mon, 28 Jul 2025 01:53:12 +0000
        try:
            clean_time = re.sub(r'\s*[+-]\d{4}$', '', utc_time_str)
            utc_dt = datetime.strptime(clean_time, '%a, %d %b %Y %H:%M:%S')
        except ValueError:
            pass
        
        # 尝试解析 ISO 格式: 2025-07-28T01:53:12Z
        if not utc_dt:
            try:
                clean_time = utc_time_str.replace('Z', '').replace('T', ' ')
                utc_dt = datetime.strptime(clean_time, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                pass
        
        # 尝试解析简单格式: 2025-07-28 01:53:12
        if not utc_dt:
            try:
                utc_dt = datetime.strptime(utc_time_str, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                pass
        
        if utc_dt:
            # 设置为UTC时区
            utc_dt = utc_dt.replace(tzinfo=timezone.utc)
            # 转换为北京时间 (UTC+8)
            beijing_tz = timezone(timedelta(hours=8))
            beijing_dt = utc_dt.astimezone(beijing_tz)
            # 格式化为易读的格式
            return beijing_dt.strftime('%Y-%m-%d %H:%M:%S (北京时间)')
        else:
            # 如果解析失败，返回原始字符串
            return f"{utc_time_str} (UTC)"
            
    except Exception as e:
        logger.warning(f"时间转换失败: {e}")
        return f"{utc_time_str} (UTC)"



def extract_verification_codes(text_content: str) -> list:
    """
    从文本内容中提取6位数字验证码
    
    Args:
        text_content: 要搜索的文本内容
    
    Returns:
        list: 找到的验证码列表
    """
    if not text_content:
        return []

    codes = []

    # 专门提取6位数字验证码的模式（优化版）
    patterns = [
        # 带空格的6位数字验证码：4 0 9 8 2 1
        r'(?:one-time code is|verification code is|verification code|验证码|code|验证码为|您的验证码)[：:\s]*([0-9]\s+[0-9]\s+[0-9]\s+[0-9]\s+[0-9]\s+[0-9])',
        r'(?:您的验证码是|your\s*verification\s*code\s*is|your\s*one-time\s*code\s*is)[：:\s]*([0-9]\s+[0-9]\s+[0-9]\s+[0-9]\s+[0-9]\s+[0-9])',

        # 连续的6位数字验证码：696691
        r'(?:one-time code is|verification code is|verification code|验证码|code|验证码为|您的验证码)[：:\s]*([0-9]{6})',
        r'(?:您的验证码是|your\s*verification\s*code\s*is|your\s*one-time\s*code\s*is)[：:\s]*([0-9]{6})',
        r'(?:验证码为|code\s*is)[：:\s]*([0-9]{6})',

        # 独立的6位数字（最常见）
        r'\b([0-9]{6})\b',

        # 独立的带空格6位数字
        r'\b([0-9]\s+[0-9]\s+[0-9]\s+[0-9]\s+[0-9]\s+[0-9])\b',
    ]

    for pattern in patterns:
        matches = re.findall(pattern, text_content, re.IGNORECASE)
        for match in matches:
            if match:
                # 处理带空格的验证码，去除空格
                clean_match = re.sub(r'\s+', '', match)
                if clean_match and _is_valid_6digit_code(clean_match) and clean_match not in codes:
                    codes.append(clean_match)

    return codes


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小为人类可读的格式

    Args:
        size_bytes: 文件大小（字节）

    Returns:
        str: 格式化后的文件大小字符串
    """
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)

    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1

    if i == 0:
        return f"{int(size)} {size_names[i]}"
    else:
        return f"{size:.1f} {size_names[i]}"


def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除不安全的字符

    Args:
        filename: 原始文件名

    Returns:
        str: 清理后的安全文件名
    """
    if not filename:
        return "untitled"

    # 移除或替换不安全的字符
    unsafe_chars = r'[<>:"/\\|?*]'
    safe_filename = re.sub(unsafe_chars, '_', filename)

    # 移除控制字符
    safe_filename = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', safe_filename)

    # 移除首尾空格和点
    safe_filename = safe_filename.strip(' .')

    # 确保不为空
    if not safe_filename:
        safe_filename = "untitled"

    # 限制长度
    if len(safe_filename) > 200:
        safe_filename = safe_filename[:200]

    return safe_filename


def extract_domain_from_email(email: str) -> str:
    """
    从邮箱地址中提取域名

    Args:
        email: 邮箱地址

    Returns:
        str: 域名，如果提取失败返回空字符串
    """
    if not email or '@' not in email:
        return ""

    try:
        return email.split('@')[1].strip().lower()
    except (IndexError, AttributeError):
        return ""


def is_valid_email(email: str) -> bool:
    """
    验证邮箱地址格式是否正确

    Args:
        email: 邮箱地址

    Returns:
        bool: 如果邮箱格式正确返回True，否则返回False
    """
    if not email:
        return False

    # 基本的邮箱格式验证
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """
    截断文本到指定长度

    Args:
        text: 要截断的文本
        max_length: 最大长度
        suffix: 截断后的后缀

    Returns:
        str: 截断后的文本
    """
    if not text:
        return ""

    if len(text) <= max_length:
        return text

    return text[:max_length - len(suffix)] + suffix


def get_current_timestamp() -> str:
    """
    获取当前时间戳字符串

    Returns:
        str: 格式化的当前时间字符串
    """
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')


def parse_email_subject(subject: str) -> dict:
    """
    解析邮件主题，提取有用信息

    Args:
        subject: 邮件主题

    Returns:
        dict: 包含解析结果的字典
    """
    result = {
        'original': subject,
        'cleaned': subject,
        'is_verification': False,
        'is_notification': False,
        'is_marketing': False,
        'priority': 'normal'
    }

    if not subject:
        return result

    subject_lower = subject.lower()

    # 检测验证码邮件
    verification_keywords = ['verification', 'verify', '验证', '验证码', 'code', 'otp', 'one-time']
    if any(keyword in subject_lower for keyword in verification_keywords):
        result['is_verification'] = True
        result['priority'] = 'high'

    # 检测通知邮件
    notification_keywords = ['notification', '通知', 'alert', '提醒', 'reminder']
    if any(keyword in subject_lower for keyword in notification_keywords):
        result['is_notification'] = True
        result['priority'] = 'medium'

    # 检测营销邮件
    marketing_keywords = ['promotion', '促销', 'sale', '优惠', 'discount', 'offer', '广告']
    if any(keyword in subject_lower for keyword in marketing_keywords):
        result['is_marketing'] = True
        result['priority'] = 'low'

    # 清理主题（移除多余的空格和特殊字符）
    result['cleaned'] = re.sub(r'\s+', ' ', subject).strip()

    return result


def clean_html_content(html_content: str) -> str:
    """
    清理HTML内容，移除不安全的标签和脚本

    Args:
        html_content: 原始HTML内容

    Returns:
        str: 清理后的HTML内容
    """
    if not html_content:
        return ""

    # 移除脚本标签
    html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

    # 移除样式标签中的危险内容
    html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

    # 移除事件处理器属性
    event_attrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout', 'onfocus', 'onblur']
    for attr in event_attrs:
        html_content = re.sub(rf'{attr}="[^"]*"', '', html_content, flags=re.IGNORECASE)
        html_content = re.sub(rf"{attr}='[^']*'", '', html_content, flags=re.IGNORECASE)

    # 移除javascript: 链接
    html_content = re.sub(r'href="javascript:[^"]*"', 'href="#"', html_content, flags=re.IGNORECASE)
    html_content = re.sub(r"href='javascript:[^']*'", "href='#'", html_content, flags=re.IGNORECASE)

    return html_content


def extract_text_from_html(html_content: str) -> str:
    """
    从HTML内容中提取纯文本

    Args:
        html_content: HTML内容

    Returns:
        str: 提取的纯文本
    """
    if not html_content:
        return ""

    # 移除HTML标签
    text = re.sub(r'<[^>]+>', '', html_content)

    # 解码HTML实体
    html_entities = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#39;': "'",
        '&nbsp;': ' ',
        '&copy;': '©',
        '&reg;': '®',
        '&trade;': '™'
    }

    for entity, char in html_entities.items():
        text = text.replace(entity, char)

    # 清理多余的空白字符
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()

    return text


def generate_random_string(length: int = 8, include_numbers: bool = True, include_uppercase: bool = False) -> str:
    """
    生成随机字符串

    Args:
        length: 字符串长度
        include_numbers: 是否包含数字
        include_uppercase: 是否包含大写字母

    Returns:
        str: 生成的随机字符串
    """
    import random
    import string

    chars = string.ascii_lowercase
    if include_numbers:
        chars += string.digits
    if include_uppercase:
        chars += string.ascii_uppercase

    return ''.join(random.choices(chars, k=length))
