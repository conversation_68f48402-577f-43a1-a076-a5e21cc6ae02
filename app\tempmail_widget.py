from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit,
    QTextEdit, QListWidget, QListWidgetItem, QSplitter,
    QGroupBox, QMessageBox, QGridLayout, QApplication,
    QMenu, QComboBox, QDialog, QTableWidget, QTableWidgetItem, QHeaderView,
    QTabWidget, QProgressBar
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QAction
import json
import os
from .tempmail_manager import TempMailManager
from .logger import logger, LogLevel
from .utils import (
    convert_utc_to_beijing, extract_verification_codes,
    sanitize_filename, extract_domain_from_email,
    is_valid_email, truncate_text, parse_email_subject,
    clean_html_content, extract_text_from_html, generate_random_string,
    get_current_timestamp
)


class MonitoringProgressDialog(QDialog):
    """监控启动进度对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("启动邮箱监控")
        self.setModal(True)
        self.setFixedSize(400, 150)
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        self.title_label = QLabel("🔄 正在启动邮箱监控...")
        self.title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        layout.addWidget(self.title_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                color: #2c3e50;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background-color: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #3498db, stop: 1 #2980b9);
                border-radius: 6px;
            }
        """)
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("准备启动...")
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                padding: 5px;
            }
        """)
        layout.addWidget(self.status_label)

    def update_progress(self, value: int, status: str):
        """更新进度（优化流畅度）"""
        # 使用动画效果更新进度条
        if hasattr(self, '_last_value'):
            # 如果进度值变化较大，使用平滑过渡
            if abs(value - self._last_value) > 10:
                self._animate_progress(self._last_value, value)
            else:
                self.progress_bar.setValue(value)
        else:
            self.progress_bar.setValue(value)

        self._last_value = value
        self.status_label.setText(status)

        # 强制立即更新界面
        self.progress_bar.repaint()
        self.status_label.repaint()
        QApplication.processEvents()

    def _animate_progress(self, start_value: int, end_value: int):
        """平滑动画更新进度条"""
        try:
            # 简单的步进动画
            step = 1 if end_value > start_value else -1
            current = start_value

            while current != end_value:
                current += step
                if (step > 0 and current > end_value) or (step < 0 and current < end_value):
                    current = end_value

                self.progress_bar.setValue(current)
                self.progress_bar.repaint()
                QApplication.processEvents()

                # 非常短的延迟，创造平滑效果
                import time
                time.sleep(0.001)  # 1毫秒

        except Exception:
            # 如果动画失败，直接设置最终值
            self.progress_bar.setValue(end_value)

    def set_title(self, title: str):
        """设置标题"""
        self.title_label.setText(title)
        QApplication.processEvents()


class MailboxFormDialog(QDialog):
    """邮箱表单对话框"""

    def __init__(self, parent=None, email="", pin="", title="添加邮箱", temp_mail_manager=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(450, 280)
        self.email_value = email
        self.pin_value = pin
        self.temp_mail_manager = temp_mail_manager
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 表单区域
        form_layout = QGridLayout()
        form_layout.setSpacing(10)

        # 域名
        form_layout.addWidget(QLabel("域名:"), 0, 0)
        self.domain_edit = QLineEdit()
        self.domain_edit.setPlaceholderText("例如: example.com")
        form_layout.addWidget(self.domain_edit, 0, 1)

        # 临时邮箱
        form_layout.addWidget(QLabel("临时邮箱:"), 1, 0)
        self.temp_email_edit = QLineEdit()
        self.temp_email_edit.setText(self.email_value)
        self.temp_email_edit.setPlaceholderText("例如: <EMAIL>")
        form_layout.addWidget(self.temp_email_edit, 1, 1)

        # PIN码
        form_layout.addWidget(QLabel("PIN码:"), 2, 0)
        self.pin_edit = QLineEdit()
        self.pin_edit.setText(self.pin_value)
        self.pin_edit.setPlaceholderText("可选，用于邮箱验证")
        form_layout.addWidget(self.pin_edit, 2, 1)

        layout.addLayout(form_layout)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setDefault(True)
        button_layout.addWidget(ok_btn)

        layout.addLayout(button_layout)

        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QLabel {
                font-weight: bold;
                color: #495057;
            }
            QLineEdit {
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
            }
            QPushButton {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                min-width: 80px;
            }
            QPushButton[text="确定"] {
                background-color: #007bff;
                color: white;
            }
            QPushButton[text="确定"]:hover {
                background-color: #0056b3;
            }
            QPushButton[text="取消"] {
                background-color: #6c757d;
                color: white;
            }
            QPushButton[text="取消"]:hover {
                background-color: #545b62;
            }
        """)

    def get_values(self):
        """获取表单值"""
        domain = self.domain_edit.text().strip()
        temp_email = self.temp_email_edit.text().strip()
        pin = self.pin_edit.text().strip()
        return domain, temp_email, pin

    def accept(self):
        """确定按钮处理"""
        domain, temp_email, pin = self.get_values()

        # 验证临时邮箱地址（必填）
        if not temp_email:
            QMessageBox.warning(self, "错误", "请输入临时邮箱地址！")
            return

        # 验证临时邮箱格式
        if "@" not in temp_email:
            QMessageBox.warning(self, "错误", "请输入有效的临时邮箱地址！")
            return

        # 域名是可选的，不需要验证

        super().accept()



class EmailListWidget(QListWidget):
    """邮件列表组件"""

    def __init__(self, temp_mail_manager=None):
        super().__init__()
        self.temp_mail_manager = temp_mail_manager
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        # 使用简化的样式表，减少启动时的样式计算
        self.setStyleSheet("""
            QListWidget {
                background: #fafafa;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                font-size: 13px;
                outline: none;
            }
            QListWidget::item {
                background: transparent;
                border-bottom: 1px solid #f0f0f0;
                padding: 2px;
                margin: 1px;
            }
            QListWidget::item:selected {
                background: #e3f2fd;
                border-left: 3px solid #2196f3;
            }
            QListWidget::item:hover {
                background: #f8f9fa;
            }
        """)
    
    def add_email(self, email_data: dict):
        """添加邮件到列表"""
        try:
            item = QListWidgetItem()

            # 先获取邮件信息
            sender = email_data.get('from', '未知发件人')
            from_name = email_data.get('from_name', '')
            subject = email_data.get('subject', '无主题')
            # 转换UTC时间为北京时间
            raw_time = email_data.get('time', get_current_timestamp())
            time_str = convert_utc_to_beijing(raw_time)
            is_new = email_data.get('is_new', False)
            attachment_count = email_data.get('attachment_count', 0)

            # 解析邮件主题，获取邮件类型信息
            subject_info = parse_email_subject(subject)

            # 使用工具函数截断显示的发件人名称
            display_sender = from_name if from_name else sender
            display_sender = truncate_text(display_sender, 15)

            # 调试信息
            logger.debug(f"📧 添加邮件到列表: {subject} - {display_sender}")

            # 创建紧凑的邮件项widget，根据已读未读状态设置不同背景
            widget = QWidget()
            if is_new:
                # 未读邮件：浅蓝色背景
                widget.setStyleSheet("""
                    QWidget {
                        background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                            stop: 0 #f3f8ff, stop: 1 #ffffff);
                        border: 1px solid #e3f2fd;
                        border-left: 3px solid #2196f3;
                        border-radius: 4px;
                        margin: 2px;
                    }
                """)
            else:
                # 已读邮件：普通背景
                widget.setStyleSheet("""
                    QWidget {
                        background: #ffffff;
                        border: 1px solid #f0f0f0;
                        border-left: 3px solid #e0e0e0;
                        border-radius: 4px;
                        margin: 2px;
                    }
                """)

            layout = QHBoxLayout(widget)
            layout.setContentsMargins(8, 3, 8, 3)  # 优化边距
            layout.setSpacing(6)



            # 主要内容区域
            content_layout = QHBoxLayout()
            content_layout.setContentsMargins(0, 0, 0, 0)
            content_layout.setSpacing(8)

            # 左侧：新邮件指示器 + 主题
            left_layout = QHBoxLayout()
            left_layout.setContentsMargins(0, 0, 0, 0)
            left_layout.setSpacing(6)

            # 邮件类型和状态指示器
            if is_new:
                if subject_info['is_verification']:
                    # 验证码邮件 - 红色高优先级
                    indicator = QLabel("🔑")
                    indicator.setToolTip("验证码邮件 (新)")
                    indicator.setStyleSheet("color: #ff4444; font-size: 12px;")
                elif subject_info['is_notification']:
                    # 通知邮件 - 橙色中优先级
                    indicator = QLabel("🔔")
                    indicator.setToolTip("通知邮件 (新)")
                    indicator.setStyleSheet("color: #ff9800; font-size: 12px;")
                else:
                    # 普通新邮件
                    indicator = QLabel("NEW")
                    indicator.setStyleSheet("""
                        background-color: #ff4444;
                        color: white;
                        font-size: 8px;
                        font-weight: bold;
                        padding: 2px 4px;
                        border-radius: 8px;
                        font-family: Arial, sans-serif;
                    """)
                    indicator.setFixedHeight(16)
                    indicator.setToolTip("新邮件")
                indicator.setFixedWidth(24)
                indicator.setAlignment(Qt.AlignCenter)
                left_layout.addWidget(indicator)
            else:
                # 已读邮件根据类型显示不同图标
                if subject_info['is_verification']:
                    indicator = QLabel("🔑")
                    indicator.setToolTip("验证码邮件")
                    indicator.setStyleSheet("color: #bbb; font-size: 10px;")
                elif subject_info['is_notification']:
                    indicator = QLabel("🔔")
                    indicator.setToolTip("通知邮件")
                    indicator.setStyleSheet("color: #bbb; font-size: 10px;")
                elif subject_info['is_marketing']:
                    indicator = QLabel("📢")
                    indicator.setToolTip("营销邮件")
                    indicator.setStyleSheet("color: #bbb; font-size: 10px;")
                else:
                    indicator = QLabel("●")
                    indicator.setToolTip("已读邮件")
                    indicator.setStyleSheet("color: #bbb; font-size: 8px;")
                indicator.setFixedWidth(24)
                indicator.setAlignment(Qt.AlignCenter)
                left_layout.addWidget(indicator)

            # 主题标签 - 根据邮件类型和状态设置样式
            display_subject = truncate_text(subject_info['cleaned'], 30)
            subject_label = QLabel(display_subject)

            if is_new:
                if subject_info['is_verification']:
                    # 验证码邮件 - 红色高亮
                    subject_label.setStyleSheet("""
                        font-weight: bold;
                        color: #d32f2f;
                        font-size: 12px;
                        padding: 1px 0;
                        background-color: rgba(244, 67, 54, 0.1);
                        border-radius: 3px;
                        padding-left: 4px;
                    """)
                elif subject_info['is_notification']:
                    # 通知邮件 - 橙色
                    subject_label.setStyleSheet("""
                        font-weight: bold;
                        color: #f57c00;
                        font-size: 12px;
                        padding: 1px 0;
                        background-color: rgba(255, 152, 0, 0.1);
                        border-radius: 3px;
                        padding-left: 4px;
                    """)
                else:
                    # 普通新邮件 - 蓝色
                    subject_label.setStyleSheet("""
                        font-weight: bold;
                        color: #0d47a1;
                        font-size: 12px;
                        padding: 1px 0;
                        background-color: rgba(33, 150, 243, 0.1);
                        border-radius: 3px;
                        padding-left: 4px;
                    """)
            else:
                # 已读邮件
                if subject_info['is_marketing']:
                    # 营销邮件 - 灰色淡化
                    subject_label.setStyleSheet("""
                        font-weight: normal;
                        color: #999;
                        font-size: 12px;
                        padding: 1px 0;
                        padding-left: 4px;
                    """)
                else:
                    subject_label.setStyleSheet("""
                        font-weight: normal;
                        color: #666;
                        font-size: 12px;
                        padding: 1px 0;
                        padding-left: 4px;
                    """)

            subject_label.setMinimumWidth(150)
            subject_label.setMaximumWidth(200)
            subject_label.setWordWrap(False)
            subject_label.setToolTip(f"主题: {subject}\n类型: {'验证码' if subject_info['is_verification'] else '通知' if subject_info['is_notification'] else '营销' if subject_info['is_marketing'] else '普通'}")
            left_layout.addWidget(subject_label)

            # 附件指示器
            if attachment_count > 0:
                attachment_label = QLabel("📎")
                attachment_label.setStyleSheet("""
                    color: #ff9800;
                    font-size: 11px;
                    padding: 0px 2px;
                """)
                attachment_label.setFixedWidth(16)
                attachment_label.setAlignment(Qt.AlignCenter)
                attachment_label.setToolTip(f"有 {attachment_count} 个附件")
                left_layout.addWidget(attachment_label)
            else:
                # 占位符保持对齐
                spacer2 = QLabel("")
                spacer2.setFixedWidth(16)
                left_layout.addWidget(spacer2)

            content_layout.addLayout(left_layout, 1)

            # 中间：发件人标签
            sender_label = QLabel(display_sender)
            if is_new:
                sender_label.setStyleSheet("""
                    color: #1976d2;
                    font-size: 10px;
                    background: #e3f2fd;
                    padding: 3px 8px;
                    border-radius: 12px;
                    border: 1px solid #2196f3;
                    font-weight: bold;
                """)
            else:
                sender_label.setStyleSheet("""
                    color: #666;
                    font-size: 10px;
                    background: #f5f5f5;
                    padding: 3px 8px;
                    border-radius: 12px;
                    border: 1px solid #ddd;
                """)
            sender_label.setFixedWidth(100)
            sender_label.setAlignment(Qt.AlignCenter)
            sender_label.setToolTip(f"发件人: {sender}")
            content_layout.addWidget(sender_label)

            # 右侧：时间
            # 如果是北京时间格式，只显示时间部分；否则显示原始格式
            if '北京时间' in time_str:
                # 从 "2025-07-28 09:53:12 (北京时间)" 中提取 "09:53"
                try:
                    time_part = time_str.split(' ')[1]  # 获取时间部分 "09:53:12"
                    time_display = time_part[:5]  # 只显示 "09:53"
                except:
                    time_display = time_str
            else:
                time_display = time_str.split(' ')[1] if ' ' in time_str else time_str
            time_label = QLabel(time_display)
            time_label.setStyleSheet("""
                color: #999;
                font-size: 10px;
                font-family: 'Consolas', monospace;
                padding: 1px 4px;
            """)
            time_label.setFixedWidth(50)
            time_label.setAlignment(Qt.AlignCenter)
            time_label.setToolTip(f"时间: {time_str}")
            content_layout.addWidget(time_label)

            layout.addLayout(content_layout, 1)

            # 右侧操作按钮
            button_layout = QVBoxLayout()
            button_layout.setContentsMargins(0, 0, 0, 0)
            button_layout.setSpacing(2)

            # 查看按钮 - 使用浅色背景深色文字
            view_btn = QPushButton("查看")
            view_btn.setFixedSize(36, 18)
            view_btn.setToolTip("查看邮件详情")
            view_btn.clicked.connect(lambda: self.itemClicked.emit(item))
            view_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e3f2fd;
                    color: #1976d2;
                    border: 1px solid #2196f3;
                    border-radius: 9px;
                    font-size: 10px;
                    font-weight: bold;
                    font-family: 'Microsoft YaHei', sans-serif;
                }
                QPushButton:hover {
                    background-color: #bbdefb;
                    color: #0d47a1;
                }
                QPushButton:pressed {
                    background-color: #90caf9;
                    color: #0d47a1;
                }
            """)
            button_layout.addWidget(view_btn)

            # 删除按钮 - 使用浅色背景深色文字
            delete_btn = QPushButton("删除")
            delete_btn.setFixedSize(36, 18)
            delete_btn.setToolTip("删除这封邮件")
            delete_btn.clicked.connect(lambda: self.delete_email(item))
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ffebee;
                    color: #d32f2f;
                    border: 1px solid #f44336;
                    border-radius: 9px;
                    font-size: 10px;
                    font-weight: bold;
                    font-family: 'Microsoft YaHei', sans-serif;
                }
                QPushButton:hover {
                    background-color: #ffcdd2;
                    color: #b71c1c;
                }
                QPushButton:pressed {
                    background-color: #ef9a9a;
                    color: #b71c1c;
                }
            """)
            button_layout.addWidget(delete_btn)

            layout.addLayout(button_layout)

            # 设置合适的item高度
            widget.setMinimumHeight(40)
            widget.setMaximumHeight(60)

            # 添加悬停预览功能
            preview_text = subject
            if len(preview_text) > 50:
                preview_text = preview_text[:50] + "..."

            widget.setToolTip(f"""
            <div style="max-width: 300px;">
                <b>主题:</b> {subject}<br>
                <b>发件人:</b> {display_sender}<br>
                <b>时间:</b> {time_str}<br>
                <b>状态:</b> {'新邮件' if is_new else '已读'}<br>
                {f'<b>附件:</b> {attachment_count} 个<br>' if attachment_count > 0 else ''}
            </div>
            """)

            # 确保item大小正确
            item.setSizeHint(widget.sizeHint())
            item.setData(Qt.UserRole, email_data)

            # 添加到列表顶部（最新邮件在上面）
            self.insertItem(0, item)
            self.setItemWidget(item, widget)

            # 调试信息
            logger.debug(f"✅ 邮件已添加到列表，当前列表项数: {self.count()}")

        except Exception as e:
            logger.error(f"❌ 添加邮件到列表失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    def add_test_email(self):
        """添加测试邮件（用于调试）"""
        test_email = {
            'from': '<EMAIL>',
            'from_name': '测试发件人',
            'subject': '这是一封测试邮件',
            'time': get_current_timestamp(),
            'is_new': True,
            'attachment_count': 0,
            'id': 12345
        }
        self.add_email(test_email)
        logger.info("📧 已添加测试邮件")

    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.itemAt(position)
        if not item:
            return

        menu = QMenu(self)

        # 删除邮件动作
        delete_action = QAction("🗑️ 删除此邮件", self)
        delete_action.triggered.connect(lambda: self.delete_email(item))
        menu.addAction(delete_action)

        # 批量删除动作
        batch_delete_action = QAction("🗑️ 删除此邮件及之后的所有邮件", self)
        batch_delete_action.triggered.connect(lambda: self.batch_delete_from_email(item))
        menu.addAction(batch_delete_action)

        menu.addSeparator()

        # 清空所有邮件
        clear_all_action = QAction("🗑️ 清空所有邮件", self)
        clear_all_action.triggered.connect(self.clear_all_emails)
        menu.addAction(clear_all_action)

        menu.addSeparator()

        # 查看详情动作
        detail_action = QAction("📄 查看详情", self)
        detail_action.triggered.connect(lambda: self.itemClicked.emit(item))
        menu.addAction(detail_action)

        menu.exec(self.mapToGlobal(position))

    def delete_email(self, item):
        """删除邮件"""
        if not self.temp_mail_manager:
            return

        email_data = item.data(Qt.UserRole)
        mail_id = email_data.get('id')
        subject = email_data.get('subject', '无主题')

        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除邮件吗？\n\n主题: {subject}",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 调用删除API（传递正确的参数）
            success = self.temp_mail_manager.api.delete_message(
                email=self.temp_mail_manager.current_email,
                mail_id=mail_id
            )

            if success:
                # 从列表中移除
                row = self.row(item)
                # 获取父窗口的日志方法
                main_widget = self.parent()
                while main_widget and not hasattr(main_widget, 'log_message'):
                    main_widget = main_widget.parent()

                if main_widget:
                    main_widget.log_message(f"🔍 删除邮件 - 行号: {row}, 总数: {self.count()}")

                    if row >= 0:
                        removed_item = self.takeItem(row)
                        if removed_item:
                            main_widget.log_message(f"✅ 邮件已从界面移除: {subject} (行号: {row})")
                            main_widget.log_message(f"📊 删除后邮件数量: {self.count()}")
                        else:
                            main_widget.log_message(f"❌ takeItem失败，尝试直接刷新列表")
                            # 如果takeItem失败，直接刷新整个列表
                            self.refresh_email_list()
                    else:
                        main_widget.log_message(f"❌ 无效的行号: {row}")

                QMessageBox.information(self, "成功", "邮件删除成功")
            else:
                QMessageBox.warning(self, "错误", "删除邮件失败")

    def batch_delete_from_email(self, item):
        """批量删除邮件（从指定邮件开始）"""
        if not self.temp_mail_manager:
            return

        email_data = item.data(Qt.UserRole)
        mail_id = email_data.get('id')
        subject = email_data.get('subject', '无主题')

        # 确认批量删除
        reply = QMessageBox.question(
            self, "确认批量删除",
            f"确定要删除此邮件及之后的所有邮件吗？\n\n起始邮件: {subject}\n\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 调用批量删除API
            success = self.temp_mail_manager.api.delete_messages_from_id(
                email=self.temp_mail_manager.current_email,
                first_id=mail_id
            )

            if success:
                # 刷新邮件列表
                self.refresh_email_list()
                QMessageBox.information(self, "成功", "批量删除成功")
            else:
                QMessageBox.warning(self, "错误", "批量删除失败")

    def clear_all_emails(self):
        """清空所有邮件"""
        if not self.temp_mail_manager:
            return

        # 确认清空
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空所有邮件吗？\n\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 调用清空API
            success = self.temp_mail_manager.api.clear_all_messages(
                email=self.temp_mail_manager.current_email
            )

            if success:
                # 清空列表并刷新
                self.clear()
                self.refresh_email_list()
                QMessageBox.information(self, "成功", "所有邮件已清空")
            else:
                QMessageBox.warning(self, "错误", "清空邮件失败")

    def refresh_email_list(self):
        """刷新邮件列表（优化流畅度）"""
        if not self.temp_mail_manager:
            return

        try:
            # 异步刷新，避免阻塞UI
            QTimer.singleShot(5, self._do_refresh_async)

        except Exception as e:
            logger.error(f"❌ 刷新邮件列表失败: {e}")

    def _do_refresh_async(self):
        """异步执行邮件列表刷新"""
        try:
            # 暂时禁用更新，提高性能
            self.setUpdatesEnabled(False)

            # 清空当前列表
            self.clear()

            # 重新获取邮件
            messages = self.temp_mail_manager.get_messages()

            # 批量添加邮件，减少重绘次数
            for i, message in enumerate(messages):
                self.add_email(message)

                # 每添加5个邮件就处理一次事件，保持响应性
                if i % 5 == 0:
                    QApplication.processEvents()

        except Exception as e:
            logger.error(f"❌ 异步刷新邮件列表失败: {e}")

        finally:
            # 重新启用更新
            self.setUpdatesEnabled(True)
            # 强制重绘
            self.repaint()


class EmailDetailWidget(QTextEdit):
    """邮件详情显示组件"""
    
    def __init__(self):
        super().__init__()
        self.setReadOnly(True)
        self.setStyleSheet("""
            QTextEdit {
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 16px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        """)
        self.setPlaceholderText("选择一封邮件查看详情...")
    
    def show_email(self, email_data: dict, temp_mail_manager=None):
        """显示邮件详情"""
        if not email_data:
            self.clear()
            return

        # 如果有邮件ID且有管理器，尝试获取详细内容
        mail_id = email_data.get('id')
        detailed_content = None

        if mail_id and temp_mail_manager:
            try:
                detailed_content = temp_mail_manager.api.get_message_detail(mail_id=mail_id)
            except Exception as e:
                print(f"获取邮件详情失败: {e}")

        # 使用详细内容或基本信息
        if detailed_content and detailed_content.get('id'):
            # 使用详细内容
            raw_html_content = detailed_content.get('html_content', '')
            text_content = detailed_content.get('text_content', '')

            # 优先使用HTML内容，但要清理安全性问题
            if raw_html_content:
                content = clean_html_content(raw_html_content)
            elif text_content:
                # 如果只有文本内容，转换为HTML格式显示
                content = text_content.replace('\n', '<br>')
            else:
                content = '无内容'

            to_email = detailed_content.get('to', '未知')
            date_info = detailed_content.get('date', email_data.get('time', '未知'))
            message_id = detailed_content.get('message_id', '')
            is_tls = detailed_content.get('is_tls', False)
        else:
            # 使用基本信息
            content = email_data.get('content', '点击邮件获取详细内容...')
            to_email = '未知'
            date_info = email_data.get('time', '未知')
            message_id = ''
            is_tls = False

        # 发件人信息
        sender = email_data.get('from', '未知')
        from_name = email_data.get('from_name', '')
        display_sender = f"{from_name} &lt;{sender}&gt;" if from_name else sender

        # 附件信息
        attachment_count = email_data.get('attachment_count', 0)
        attachment_info = f"📎 {attachment_count} 个附件" if attachment_count > 0 else ""

        # 验证码提取 - 优先从text内容提取，否则从html内容提取
        verification_codes = []
        if detailed_content:
            # 优先使用text内容
            text_content = detailed_content.get('text_content', '')
            if text_content:
                verification_codes = extract_verification_codes(text_content)

            # 如果text内容没有找到验证码，尝试从html内容提取
            if not verification_codes:
                html_content_raw = detailed_content.get('html_content', '')
                if html_content_raw:
                    # 使用工具函数从HTML内容中提取纯文本
                    text_from_html = extract_text_from_html(html_content_raw)
                    verification_codes = extract_verification_codes(text_from_html)

        # 验证码信息
        codes_info = ""
        if verification_codes:
            codes_list = ", ".join(verification_codes)
            codes_info = f'<p style="margin: 4px 0; color: #28a745; font-weight: bold;"><strong>🔑 验证码:</strong> {codes_list}</p>'

        html_content = f"""
        <div style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6;">
            <div style="border-bottom: 2px solid #e9ecef; padding-bottom: 16px; margin-bottom: 16px;">
                <h3 style="color: #2c3e50; margin: 0 0 8px 0;">📧 {email_data.get('subject', '无主题')}</h3>
                <p style="margin: 4px 0; color: #6c757d;">
                    <strong>发件人:</strong> {display_sender}
                </p>
                <p style="margin: 4px 0; color: #6c757d;">
                    <strong>收件人:</strong> {to_email}
                </p>
                <p style="margin: 4px 0; color: #6c757d;">
                    <strong>时间:</strong> {convert_utc_to_beijing(date_info)}
                </p>
                {f'<p style="margin: 4px 0; color: #6c757d;"><strong>{attachment_info}</strong></p>' if attachment_info else ''}
                {codes_info}
            </div>
            <div style="background: #f8f9fa; padding: 16px; border-radius: 8px;">
                <h4 style="color: #495057; margin: 0 0 12px 0;">📄 邮件内容:</h4>
                <div style="background: white; padding: 16px; border-radius: 6px; border: 1px solid #dee2e6;">
                    {content}
                </div>
            </div>
        </div>
        """
        self.setHtml(html_content)




class TempMailWidget(QWidget):
    """临时邮箱管理界面"""
    
    def __init__(self):
        super().__init__()
        self.temp_mail_manager = TempMailManager()
        self.config_file = "tempmail_config.json"
        self.log_entries = []  # 存储所有日志条目
        self.current_log_filter = "信息"  # 当前日志过滤级别
        self.is_initializing = True  # 标记正在初始化

        # 快速设置基础界面
        self.setup_ui()
        self.setup_connections()
        self.setup_styles()

        # 异步加载配置和启动功能，避免阻塞界面
        QTimer.singleShot(100, self.async_initialize)

    def async_initialize(self):
        """异步初始化，避免阻塞界面"""
        try:
            # 显示初始化状态
            self.status_label.setText("🔄 正在加载配置...")
            QApplication.processEvents()

            # 更快的分步骤加载，减少延迟
            QTimer.singleShot(20, self._load_config_step)  # 从50ms减少到20ms

        except Exception as e:
            logger.error(f"❌ 异步初始化失败: {e}")
            self.status_label.setText("❌ 初始化失败")
            self.is_initializing = False

    def _load_config_step(self):
        """分步加载配置"""
        try:
            # 显示加载进度
            self.status_label.setText("🔄 正在解析配置文件...")
            QApplication.processEvents()

            # 加载配置（不自动启动监控）
            self.load_config_only()

            # 更快的下一步
            QTimer.singleShot(10, self._finish_initialization)  # 从30ms减少到10ms

        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            self.status_label.setText("❌ 配置加载失败")
            self.is_initializing = False

    def _finish_initialization(self):
        """完成初始化"""
        try:
            # 更新状态
            self.status_label.setText("✅ 配置加载完成")
            QApplication.processEvents()

            # 标记初始化完成
            self.is_initializing = False

            # 检查是否需要自动启动
            if self.should_auto_start():
                # 显示即将自动启动的提示
                self.status_label.setText("⏳ 1秒后自动启动监控...")
                QTimer.singleShot(1000, self._show_auto_start_countdown)  # 减少到1秒
            else:
                self.status_label.setText("✅ 就绪")

        except Exception as e:
            logger.error(f"❌ 完成初始化失败: {e}")
            self.status_label.setText("❌ 初始化失败")
            self.is_initializing = False

    def _show_auto_start_countdown(self):
        """显示自动启动倒计时"""
        try:
            if not self.is_initializing and self.should_auto_start():
                self.status_label.setText("🚀 正在自动启动监控...")
                QApplication.processEvents()

                # 立即启动监控
                QTimer.singleShot(100, self.delayed_auto_start)
        except Exception as e:
            logger.error(f"❌ 自动启动倒计时失败: {e}")
            self.status_label.setText("✅ 就绪")
            self.is_initializing = False

    def should_auto_start(self):
        """检查是否应该自动启动监控"""
        try:
            current_index = self.email_combo.currentIndex()
            if current_index >= 0:
                item_data = self.email_combo.itemData(current_index)
                return item_data and item_data.get('email')
            return False
        except:
            return False

    def delayed_auto_start(self):
        """延迟自动启动监控（启动时显示进度条）"""
        try:
            if not self.is_initializing:
                current_index = self.email_combo.currentIndex()
                if current_index >= 0:
                    item_data = self.email_combo.itemData(current_index)
                    if item_data and item_data.get('email'):
                        email = item_data.get('email')
                        pin = item_data.get('pin', '')
                        logger.info(f"🔄 自动启动上次邮箱监控: {email}")

                        # 启动时显示进度条
                        self._startup_monitor_with_progress(email, pin)
        except Exception as e:
            logger.error(f"❌ 延迟自动启动失败: {e}")
            self.status_label.setText("就绪")

    def _startup_monitor_with_progress(self, email: str, pin: str):
        """启动时的监控设置（超流畅进度条）"""
        try:
            # 显示启动进度对话框
            progress_dialog = MonitoringProgressDialog(self)
            progress_dialog.set_title("🚀 启动邮箱监控")
            progress_dialog.show()

            # 分步骤流畅进度更新
            self._startup_step1(progress_dialog, email, pin)

        except Exception as e:
            logger.error(f"❌ 启动监控异常: {e}")
            self.status_label.setText("❌ 启动监控异常")

    def _startup_step1(self, progress_dialog, email, pin):
        """启动步骤1: 连接邮箱"""
        try:
            progress_dialog.update_progress(20, f"正在连接邮箱: {email}")
            QApplication.processEvents()

            # 异步执行下一步
            QTimer.singleShot(10, lambda: self._startup_step2(progress_dialog, email, pin))

        except Exception as e:
            progress_dialog.close()
            logger.error(f"❌ 启动步骤1失败: {e}")

    def _startup_step2(self, progress_dialog, email, pin):
        """启动步骤2: 设置邮箱"""
        try:
            progress_dialog.update_progress(40, "正在验证邮箱...")
            QApplication.processEvents()

            # 从邮箱地址中提取域名并更新API客户端
            if "@" in email:
                domain = email.split("@")[1]
                logger.info(f"🔄 启动时更新域名: {domain}")
                self.temp_mail_manager.update_domain(domain)

            # 设置邮箱
            result = self.temp_mail_manager.set_email(email, pin)

            if result.get('success'):
                QTimer.singleShot(10, lambda: self._startup_step3(progress_dialog, email))
            else:
                progress_dialog.close()
                logger.error(f"❌ 启动监控失败: {result.get('error', '未知错误')}")
                self.status_label.setText("❌ 启动监控失败")

        except Exception as e:
            progress_dialog.close()
            logger.error(f"❌ 启动步骤2失败: {e}")

    def _startup_step3(self, progress_dialog, email):
        """启动步骤3: 启动监控"""
        try:
            progress_dialog.update_progress(70, "正在启动监控线程...")
            QApplication.processEvents()

            # 启动监控
            self.temp_mail_manager.start_monitoring(email)

            QTimer.singleShot(10, lambda: self._startup_step4(progress_dialog, email))

        except Exception as e:
            progress_dialog.close()
            logger.error(f"❌ 启动步骤3失败: {e}")

    def _startup_step4(self, progress_dialog, email):
        """启动步骤4: 完成启动"""
        try:
            progress_dialog.update_progress(90, "正在初始化界面...")
            QApplication.processEvents()

            QTimer.singleShot(10, lambda: self._startup_finish(progress_dialog, email))

        except Exception as e:
            progress_dialog.close()
            logger.error(f"❌ 启动步骤4失败: {e}")

    def _startup_finish(self, progress_dialog, email):
        """完成启动"""
        try:
            progress_dialog.update_progress(100, "启动完成！")
            QApplication.processEvents()

            # 超快关闭进度条
            QTimer.singleShot(150, progress_dialog.close)

            self.status_label.setText(f"✅ 已启动监控: {email}")
            logger.info(f"🚀 启动监控成功: {email}")

        except Exception as e:
            progress_dialog.close()
            logger.error(f"❌ 完成启动失败: {e}")

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(6, 6, 6, 6)
        layout.setSpacing(4)

        # 创建tab标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                padding: 6px 12px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
            }
            QTabBar::tab:hover {
                background-color: #e9ecef;
            }
        """)

        # 邮件标签页
        self.mail_tab = self._create_mail_tab()
        self.tab_widget.addTab(self.mail_tab, "📧 邮件")

        # 配置标签页
        self.config_tab = self._create_config_tab()
        self.tab_widget.addTab(self.config_tab, "⚙️ 配置")

        # 日志标签页
        self.log_tab = self._create_log_tab()
        self.tab_widget.addTab(self.log_tab, "📋 日志")

        layout.addWidget(self.tab_widget)

        # 底部状态栏
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #6c757d; font-size: 11px; padding: 4px;")
        layout.addWidget(self.status_label)

    def _create_mail_tab(self):
        """创建邮件标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(6, 6, 6, 6)
        layout.setSpacing(6)

        # 邮箱设置区域
        email_layout = QHBoxLayout()
        email_layout.setSpacing(8)

        # 邮箱选择下拉框
        self.email_combo = QComboBox()
        self.email_combo.setEditable(True)
        self.email_combo.setPlaceholderText("选择或输入邮箱地址")
        self.email_combo.setMinimumWidth(200)
        self.email_combo.setMaxVisibleItems(10)
        self.email_combo.setSizeAdjustPolicy(QComboBox.AdjustToContents)
        # 连接切换事件，自动加载邮件
        self.email_combo.currentIndexChanged.connect(self.on_email_combo_changed)
        email_layout.addWidget(self.email_combo)



        # 监控状态显示（更突出的位置）
        self.monitor_status = QLabel("⏸️ 未监控")
        self.monitor_status.setStyleSheet("""
            color: #666;
            font-weight: bold;
            font-size: 12px;
            padding: 4px 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
            border: 1px solid #ddd;
        """)
        self.monitor_status.setMinimumWidth(80)
        email_layout.addWidget(self.monitor_status)

        email_layout.addStretch()

        # 操作按钮组
        self.copy_btn = QPushButton("📋 复制")
        self.copy_btn.clicked.connect(self.copy_email)
        self.copy_btn.setEnabled(False)
        self.copy_btn.setMaximumWidth(60)
        email_layout.addWidget(self.copy_btn)

        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.refresh_messages)
        self.refresh_btn.setEnabled(False)
        self.refresh_btn.setMaximumWidth(60)
        email_layout.addWidget(self.refresh_btn)

        layout.addLayout(email_layout)

        # 当前邮箱信息
        self.email_label = QLabel("暂无邮箱")
        self.email_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 4px 8px; background-color: #f8f9fa; border-radius: 4px; font-size: 11px;")
        layout.addWidget(self.email_label)

        # 邮件区域
        mail_splitter = QSplitter(Qt.Horizontal)

        # 邮件列表（去掉GroupBox，使用简单标签）
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        list_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        list_layout.setSpacing(3)  # 减少间距

        # 收件箱标题和按钮
        list_header = QHBoxLayout()
        list_label = QLabel("📬 收件箱")
        list_label.setStyleSheet("font-weight: bold; color: #495057; padding: 2px;")
        list_header.addWidget(list_label)

        # 先创建邮件列表
        self.email_list = EmailListWidget(self.temp_mail_manager)
        self.email_list.itemClicked.connect(self.show_email_detail)



        # 清空所有邮件按钮
        clear_all_btn = QPushButton("🗑️ 清空所有")
        clear_all_btn.setMaximumWidth(80)
        clear_all_btn.clicked.connect(lambda: self.email_list.clear_all_emails())
        clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        list_header.addWidget(clear_all_btn)

        list_header.addStretch()

        list_layout.addLayout(list_header)
        list_layout.addWidget(self.email_list)
        mail_splitter.addWidget(list_widget)

        # 邮件详情（去掉GroupBox，使用简单标签）
        detail_widget = QWidget()
        detail_layout = QVBoxLayout(detail_widget)
        detail_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        detail_layout.setSpacing(3)  # 减少间距

        detail_label = QLabel("📄 邮件详情")
        detail_label.setStyleSheet("font-weight: bold; color: #495057; padding: 2px;")
        detail_layout.addWidget(detail_label)

        self.email_detail = EmailDetailWidget()
        detail_layout.addWidget(self.email_detail)
        mail_splitter.addWidget(detail_widget)

        # 设置分割器比例（左侧收件箱列表，右侧预览区域）
        mail_splitter.setSizes([350, 650])
        layout.addWidget(mail_splitter, 1)

        return tab

    def _create_config_tab(self):
        """创建配置标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 邮箱管理区域
        mailbox_group = QGroupBox("📮 邮箱管理")
        mailbox_layout = QVBoxLayout(mailbox_group)

        # 邮箱列表表格
        self.config_table = QTableWidget()
        self.config_table.setColumnCount(4)
        self.config_table.setHorizontalHeaderLabels(["🌐 域名", "📧 临时邮箱", "🔑 PIN码", "⚙️ 操作"])

        # 设置表格自适应样式
        header = self.config_table.horizontalHeader()
        header.setStretchLastSection(False)

        # 邮箱后缀列：固定宽度120px
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        self.config_table.setColumnWidth(0, 120)

        # 邮箱地址列：占据剩余空间，最小宽度180px
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        self.config_table.setColumnWidth(1, 180)

        # PIN码列：根据内容自适应，最小宽度80px
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.config_table.setColumnWidth(2, 80)

        # 操作列：固定宽度130px
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        self.config_table.setColumnWidth(3, 130)

        # 表格外观设置
        self.config_table.setAlternatingRowColors(True)
        self.config_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.config_table.setSelectionMode(QTableWidget.SingleSelection)
        self.config_table.setMaximumHeight(500)  # 进一步增加最大高度
        self.config_table.setMinimumHeight(300)  # 进一步增加最小高度

        # 设置行高
        self.config_table.verticalHeader().setDefaultSectionSize(50)  # 增加行高到50px
        self.config_table.verticalHeader().setVisible(False)  # 隐藏行号

        # 连接双击事件
        self.config_table.itemDoubleClicked.connect(self.on_table_double_clicked)

        # 设置表格样式
        self.config_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            QTableWidget::item {
                padding: 6px 8px;
                border: none;
                font-size: 13px;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 8px;
                border: none;
                border-bottom: 2px solid #ddd;
                font-weight: bold;
                color: #333;
                font-size: 13px;
                min-height: 30px;
            }
        """)

        mailbox_layout.addWidget(self.config_table)

        # 连接表格大小变化事件
        self.config_table.resizeEvent = self.on_config_table_resize

        # 邮箱操作按钮
        button_layout = QHBoxLayout()

        add_btn = QPushButton("➕ 添加邮箱")
        add_btn.clicked.connect(self.add_mailbox_to_config)
        button_layout.addWidget(add_btn)

        remove_btn = QPushButton("➖ 删除选中")
        remove_btn.clicked.connect(self.remove_selected_mailbox)
        button_layout.addWidget(remove_btn)

        test_btn = QPushButton("🔧 测试连接")
        test_btn.clicked.connect(self.test_mailbox_connection)
        button_layout.addWidget(test_btn)

        button_layout.addStretch()
        mailbox_layout.addLayout(button_layout)

        layout.addWidget(mailbox_group)

        # 邮箱操作区域
        mailbox_ops_group = QGroupBox("📧 邮箱操作")
        mailbox_ops_layout = QVBoxLayout(mailbox_ops_group)

        # 生成邮箱区域
        generate_layout = QHBoxLayout()

        # 生成邮箱按钮
        self.generate_btn = QPushButton("🏠 生成邮箱")
        self.generate_btn.clicked.connect(self.generate_random_email)
        self.generate_btn.setMaximumWidth(100)
        self.generate_btn.setToolTip("基于当前域名生成新邮箱地址")
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        generate_layout.addWidget(self.generate_btn)

        # 生成的邮箱显示区域
        self.generated_email_label = QLabel("点击生成按钮创建新邮箱")
        self.generated_email_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 6px 12px;
                color: #6c757d;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        generate_layout.addWidget(self.generated_email_label)

        # 复制按钮
        self.copy_generated_btn = QPushButton("📋")
        self.copy_generated_btn.clicked.connect(self.copy_generated_email)
        self.copy_generated_btn.setEnabled(False)
        self.copy_generated_btn.setMaximumWidth(30)
        self.copy_generated_btn.setToolTip("复制生成的邮箱地址")
        self.copy_generated_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 6px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
            QPushButton:disabled {
                background-color: #e9ecef;
                color: #adb5bd;
            }
        """)
        generate_layout.addWidget(self.copy_generated_btn)

        mailbox_ops_layout.addLayout(generate_layout)

        # 获取验证码按钮
        self.get_code_btn = QPushButton("🔑 获取验证码")
        self.get_code_btn.clicked.connect(self.extract_verification_codes)
        self.get_code_btn.setEnabled(False)
        self.get_code_btn.setMaximumWidth(150)
        self.get_code_btn.setToolTip("从当前选中邮件中提取验证码")
        self.get_code_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """)
        mailbox_ops_layout.addWidget(self.get_code_btn)

        # 删除当前邮箱按钮
        self.delete_btn = QPushButton("🗑️ 删除当前邮箱")
        self.delete_btn.clicked.connect(self.delete_mailbox)
        self.delete_btn.setEnabled(False)
        self.delete_btn.setMaximumWidth(150)
        mailbox_ops_layout.addWidget(self.delete_btn)

        # 清空所有邮件按钮
        self.clear_all_btn = QPushButton("🧹 清空当前邮箱所有邮件")
        self.clear_all_btn.clicked.connect(self.clear_current_mailbox)
        self.clear_all_btn.setEnabled(False)
        self.clear_all_btn.setMaximumWidth(200)

        # 测试按钮（用于调试列表显示问题）
        self.test_email_btn = QPushButton("🧪 添加测试邮件")
        self.test_email_btn.clicked.connect(self.email_list.add_test_email)
        self.test_email_btn.setMaximumWidth(150)
        self.test_email_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
        """)
        mailbox_ops_layout.addWidget(self.test_email_btn)


        self.clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
        mailbox_ops_layout.addWidget(self.clear_all_btn)

        layout.addWidget(mailbox_ops_group)

        layout.addStretch()

        # 加载邮箱列表到配置表格
        self.load_mailboxes_to_config()

        return tab

    def _create_log_tab(self):
        """创建日志标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(6, 6, 6, 6)

        # 日志控制栏
        control_layout = QHBoxLayout()

        control_layout.addWidget(QLabel("级别过滤:"))

        # 日志级别过滤器
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["全部", "调试", "信息", "警告", "错误"])
        self.log_level_combo.setCurrentText("信息")
        self.log_level_combo.currentTextChanged.connect(self.on_log_level_changed)
        control_layout.addWidget(self.log_level_combo)

        control_layout.addStretch()

        # 保存按钮
        save_btn = QPushButton("� 保存")
        save_btn.clicked.connect(self.save_log)
        control_layout.addWidget(save_btn)

        # 清空按钮
        clear_btn = QPushButton("🗑️ 清空")
        clear_btn.clicked.connect(self.clear_log)
        control_layout.addWidget(clear_btn)

        layout.addLayout(control_layout)

        # 日志显示区域
        self.log_widget = QTextEdit()
        self.log_widget.setReadOnly(True)
        self.log_widget.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                color: #495057;
                padding: 4px;
            }
        """)
        layout.addWidget(self.log_widget)

        return tab
    
    def setup_connections(self):
        """设置信号连接"""
        self.temp_mail_manager.email_set.connect(self.on_email_set)
        self.temp_mail_manager.new_message_received.connect(self.on_new_message)
        self.temp_mail_manager.monitoring_started.connect(self.on_monitoring_started)
        self.temp_mail_manager.monitoring_stopped.connect(self.on_monitoring_stopped)
        self.temp_mail_manager.log_message.connect(self.log_message)  # 连接日志信号

        # 连接全局logger的信号
        logger.log_message.connect(self.log_message_with_level)



    def on_email_combo_changed(self, index):
        """邮箱下拉框切换事件处理（异步优化）"""
        if index < 0:
            return

        # 获取选中的邮箱数据
        item_data = self.email_combo.itemData(index)
        if not item_data:
            return

        email = item_data.get('email', '')
        pin = item_data.get('pin', '')

        if not email:
            return

        # 防止重复切换到同一邮箱
        current_email = self.temp_mail_manager.get_current_email()
        if current_email == email:
            return

        # 立即更新状态，给用户反馈
        self.status_label.setText(f"🔄 正在切换到: {email}")

        # 强制刷新界面，避免卡顿感
        QApplication.processEvents()

        # 显示快速进度条
        progress_dialog = MonitoringProgressDialog(self)
        progress_dialog.set_title("⚡ 切换邮箱")
        progress_dialog.show()
        progress_dialog.update_progress(20, f"正在切换到: {email}")

        # 超快异步执行，立即开始
        QTimer.singleShot(5, lambda: self._switch_email_with_progress(email, pin, progress_dialog))

    def _switch_email_with_progress(self, email: str, pin: str, progress_dialog: MonitoringProgressDialog):
        """带进度条的邮箱切换（超快版本）"""
        try:
            # 步骤1: 停止当前监控
            progress_dialog.update_progress(40, "正在停止当前监控...")
            QApplication.processEvents()

            if self.temp_mail_manager.is_monitoring():
                self.temp_mail_manager.stop_monitoring()

            # 步骤2: 设置新邮箱
            progress_dialog.update_progress(60, f"正在连接: {email}")
            QApplication.processEvents()

            # 从邮箱地址中提取域名并更新API客户端
            if "@" in email:
                domain = email.split("@")[1]
                logger.info(f"🔄 切换邮箱时更新域名: {domain}")
                self.temp_mail_manager.update_domain(domain)

            result = self.temp_mail_manager.set_email(email, pin)

            if result.get('success'):
                # 步骤3: 启动监控
                progress_dialog.update_progress(80, "正在启动监控...")
                QApplication.processEvents()

                self.temp_mail_manager.start_monitoring(email)

                # 步骤4: 完成
                progress_dialog.update_progress(100, "切换完成！")
                QApplication.processEvents()

                # 超快关闭进度条
                QTimer.singleShot(100, progress_dialog.close)

                # 异步刷新邮件列表，避免阻塞
                QTimer.singleShot(50, self.email_list.refresh_email_list)

                # 异步保存配置
                QTimer.singleShot(100, self.save_config)

                self.status_label.setText(f"✅ 已切换到: {email}")
                logger.info(f"📧 切换到邮箱: {email}")

            else:
                progress_dialog.close()
                logger.error(f"❌ 切换邮箱失败: {result.get('error', '未知错误')}")
                self.status_label.setText("❌ 邮箱切换失败")

        except Exception as e:
            progress_dialog.close()
            logger.error(f"❌ 切换邮箱异常: {e}")
            self.status_label.setText("❌ 邮箱切换异常")

    def _switch_email_async(self, email: str, pin: str):
        """异步切换邮箱（简化版本，无进度条）"""
        try:
            # 停止当前监控（快速操作）
            if self.temp_mail_manager.is_monitoring():
                self.temp_mail_manager.stop_monitoring()

            # 简单的状态提示
            self.status_label.setText(f"🔄 正在切换到: {email}")
            QApplication.processEvents()

            # 从邮箱地址中提取域名并更新API客户端
            if "@" in email:
                domain = email.split("@")[1]
                logger.info(f"🔄 异步切换时更新域名: {domain}")
                self.temp_mail_manager.update_domain(domain)

            # 设置新邮箱（可能的网络请求）
            result = self.temp_mail_manager.set_email(email, pin)

            if result.get('success'):
                logger.info(f"📧 切换到邮箱: {email}")

                # 开始监控
                self.temp_mail_manager.start_monitoring(email)

                # 加载邮件列表
                self.email_list.refresh_email_list()

                # 保存配置
                self.save_config()

                self.status_label.setText(f"✅ 已切换到: {email}")

            else:
                logger.error(f"❌ 切换邮箱失败: {result.get('error', '未知错误')}")
                self.status_label.setText("❌ 邮箱切换失败")

        except Exception as e:
            logger.error(f"❌ 切换邮箱异常: {e}")
            self.status_label.setText("❌ 邮箱切换异常")

    def clear_current_mailbox(self):
        """清空当前邮箱的所有邮件"""
        current_email = self.temp_mail_manager.get_current_email()
        if not current_email:
            QMessageBox.warning(self, "警告", "当前没有监控的邮箱！")
            return

        reply = QMessageBox.question(
            self, "确认清空",
            f"确定要清空邮箱 {current_email} 的所有邮件吗？\n\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 立即更新状态，给用户反馈
            self.status_label.setText("🗑️ 正在清空邮箱...")

            # 强制刷新界面
            QApplication.processEvents()

            # 异步执行清空操作，避免阻塞UI
            QTimer.singleShot(50, lambda: self._clear_mailbox_async(current_email))

    def _clear_mailbox_async(self, email: str):
        """异步清空邮箱"""
        try:
            success = self.temp_mail_manager.clear_all_messages()
            if success:
                # 清空本地邮件列表
                self.email_list.clear()
                self.status_label.setText("✅ 邮箱已清空")
                QMessageBox.information(self, "成功", "邮箱已清空！")
            else:
                self.status_label.setText("❌ 清空失败")
                QMessageBox.critical(self, "失败", "清空邮箱失败，请稍后重试。")
        except Exception as e:
            logger.error(f"❌ 清空邮箱异常: {e}")
            self.status_label.setText("❌ 清空异常")
            QMessageBox.critical(self, "错误", f"清空邮箱异常: {e}")

    def load_mailboxes_to_config(self):
        """加载邮箱列表到配置表格"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                mailboxes = config.get('mailboxes', [])
                self.config_table.setRowCount(len(mailboxes))

                for row, mailbox in enumerate(mailboxes):
                    if isinstance(mailbox, dict):
                        domain = mailbox.get('domain', '')
                        email = mailbox.get('email', '')
                        pin = mailbox.get('pin', '')
                    else:
                        # 兼容旧格式
                        domain = ''
                        email = mailbox
                        pin = ''

                    # 域名（可能为空）
                    self.config_table.setItem(row, 0, QTableWidgetItem(domain or ""))

                    # 临时邮箱地址
                    self.config_table.setItem(row, 1, QTableWidgetItem(email))

                    # PIN码
                    pin_display = pin if pin else "无"
                    self.config_table.setItem(row, 2, QTableWidgetItem(pin_display))

                    # 操作按钮
                    btn_widget = QWidget()
                    btn_layout = QHBoxLayout(btn_widget)
                    btn_layout.setContentsMargins(4, 4, 4, 4)  # 减少边距
                    btn_layout.setSpacing(6)

                    # 编辑按钮
                    edit_btn = QPushButton("编辑")
                    edit_btn.setFixedSize(50, 28)  # 增加按钮宽度到50px
                    edit_btn.setToolTip("编辑邮箱信息")
                    edit_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #2196f3;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            font-size: 10px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #1976d2;
                        }
                        QPushButton:pressed {
                            background-color: #1565c0;
                        }
                    """)
                    edit_btn.clicked.connect(lambda checked, r=row: self.edit_mailbox(r))
                    btn_layout.addWidget(edit_btn)

                    # 删除按钮
                    delete_btn = QPushButton("删除")
                    delete_btn.setFixedSize(50, 28)  # 增加按钮宽度到50px
                    delete_btn.setToolTip("删除邮箱")
                    delete_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #f44336;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            font-size: 10px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #d32f2f;
                        }
                        QPushButton:pressed {
                            background-color: #c62828;
                        }
                    """)
                    delete_btn.clicked.connect(lambda checked, r=row: self.delete_mailbox_from_config(r))
                    btn_layout.addWidget(delete_btn)

                    btn_layout.addStretch()  # 添加弹性空间
                    self.config_table.setCellWidget(row, 3, btn_widget)  # 操作列现在是第4列（索引3）

        except Exception as e:
            logger.error(f"加载邮箱配置失败: {e}")

    def add_mailbox_to_config(self):
        """添加邮箱到配置"""
        dialog = MailboxFormDialog(self, title="添加邮箱", temp_mail_manager=self.temp_mail_manager)
        if dialog.exec() != QDialog.Accepted:
            return

        domain, temp_email, pin = dialog.get_values()

        # 添加到配置文件
        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            mailboxes = config.get('mailboxes', [])

            new_mailbox = {
                'domain': domain.strip(),
                'email': temp_email.strip(),
                'pin': pin.strip()
            }

            # 检查是否已存在
            for mailbox in mailboxes:
                if isinstance(mailbox, dict) and mailbox.get('email') == temp_email.strip():
                    QMessageBox.warning(self, "警告", "该临时邮箱已存在！")
                    return

            mailboxes.append(new_mailbox)
            config['mailboxes'] = mailboxes

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 重新加载表格和下拉框
            self.load_mailboxes_to_config()
            self.load_config()

            QMessageBox.information(self, "成功", "邮箱添加成功！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加邮箱失败: {e}")

    def delete_mailbox_from_config(self, row):
        """从配置中删除指定行的邮箱"""
        if row < 0 or row >= self.config_table.rowCount():
            return

        email = self.config_table.item(row, 1).text()  # 邮箱地址现在在第2列（索引1）

        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除邮箱 {email} 吗？",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                config = {}
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                mailboxes = config.get('mailboxes', [])

                # 找到并删除对应的邮箱
                mailboxes = [mb for mb in mailboxes if mb.get('email') != email]

                config['mailboxes'] = mailboxes

                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

                # 重新加载表格和下拉框
                self.load_mailboxes_to_config()
                self.load_config()

                QMessageBox.information(self, "成功", f"邮箱 {email} 已删除！")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除邮箱失败: {e}")

    def on_config_table_resize(self, event):
        """配置表格大小调整事件"""
        # 调用原始的resizeEvent
        QTableWidget.resizeEvent(self.config_table, event)

        # 获取表格可用宽度
        available_width = self.config_table.viewport().width()

        # 固定列宽
        domain_width = 120
        pin_width = 80
        action_width = 130

        # 计算邮箱地址列的宽度（剩余空间）
        email_width = max(180, available_width - domain_width - pin_width - action_width - 20)  # 20px用于边距

        # 设置列宽
        self.config_table.setColumnWidth(0, domain_width)  # 域名列
        self.config_table.setColumnWidth(1, email_width)   # 邮箱地址列
        self.config_table.setColumnWidth(2, pin_width)     # PIN码列
        self.config_table.setColumnWidth(3, action_width)  # 操作列

    def remove_selected_mailbox(self):
        """删除选中的邮箱"""
        current_row = self.config_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要删除的邮箱！")
            return

        email = self.config_table.item(current_row, 0).text()

        reply = QMessageBox.question(self, "确认删除",
                                   f"确定要删除邮箱 {email} 吗？",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            try:
                config = {}
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                mailboxes = config.get('mailboxes', [])
                # 删除对应的邮箱
                mailboxes = [mb for mb in mailboxes
                           if (isinstance(mb, dict) and mb.get('email') != email) or
                              (isinstance(mb, str) and mb != email)]

                config['mailboxes'] = mailboxes

                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

                # 重新加载
                self.load_mailboxes_to_config()
                self.load_config()

                QMessageBox.information(self, "成功", "邮箱删除成功！")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除邮箱失败: {e}")

    def on_table_double_clicked(self, item):
        """处理表格双击事件"""
        if item is None:
            return

        # 获取双击的行号
        row = item.row()

        # 调用编辑邮箱方法
        self.edit_mailbox(row)

    def edit_mailbox(self, row):
        """编辑邮箱"""
        # 从正确的列获取信息：列0是域名，列1是临时邮箱，列2是PIN码
        domain = self.config_table.item(row, 0).text()  # 域名在第0列
        email = self.config_table.item(row, 1).text()  # 临时邮箱在第1列
        current_pin = self.config_table.item(row, 2).text()  # PIN码在第2列
        if current_pin == "无":
            current_pin = ""

        # 创建表单对话框，需要传递域名信息
        dialog = MailboxFormDialog(self, email=email, pin=current_pin, title="编辑邮箱", temp_mail_manager=self.temp_mail_manager)

        # 设置域名字段
        dialog.domain_edit.setText(domain)
        if dialog.exec() != QDialog.Accepted:
            return

        new_domain, new_email, new_pin = dialog.get_values()

        # 如果邮箱地址改变了，需要检查是否已存在
        if new_email != email:
            try:
                config = {}
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                mailboxes = config.get('mailboxes', [])
                for mailbox in mailboxes:
                    existing_email = mailbox.get('email') if isinstance(mailbox, dict) else mailbox
                    if existing_email == new_email:
                        QMessageBox.warning(self, "警告", "该邮箱地址已存在！")
                        return
            except Exception as e:
                QMessageBox.critical(self, "错误", f"检查邮箱失败: {e}")
                return

        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            mailboxes = config.get('mailboxes', [])

            for i, mailbox in enumerate(mailboxes):
                if isinstance(mailbox, dict) and mailbox.get('email') == email:
                    mailboxes[i]['domain'] = new_domain
                    mailboxes[i]['email'] = new_email
                    mailboxes[i]['pin'] = new_pin
                    break
                elif isinstance(mailbox, str) and mailbox == email:
                    mailboxes[i] = {
                        'domain': new_domain,
                        'email': new_email,
                        'pin': new_pin
                    }
                    break

            config['mailboxes'] = mailboxes

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            # 重新加载
            self.load_mailboxes_to_config()
            self.load_config()

            QMessageBox.information(self, "成功", "邮箱信息更新成功！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"更新邮箱信息失败: {e}")

    def on_table_double_clicked(self, item):
        """处理表格双击事件"""
        if item is None:
            return

        # 获取双击的行号
        row = item.row()

        # 调用编辑邮箱方法
        self.edit_mailbox(row)

    def test_mailbox_connection(self):
        """测试邮箱连接"""
        current_row = self.config_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "警告", "请先选择要测试的邮箱！")
            return

        # 从正确的列获取信息：列0是域名，列1是邮箱地址，列2是PIN码
        email = self.config_table.item(current_row, 1).text()  # 邮箱地址在第1列
        pin_text = self.config_table.item(current_row, 2).text()  # PIN码在第2列
        pin = pin_text if pin_text != "无" else ""

        # 测试邮箱连接
        try:
            self.status_label.setText(f"🔄 正在测试邮箱: {email}")
            QApplication.processEvents()

            result = self.temp_mail_manager.api.set_email(email, pin)

            if result.get('success'):
                QMessageBox.information(self, "测试结果", f"✅ 邮箱 {email} 连接成功！\n\n{result.get('message', '')}")
                self.status_label.setText("✅ 邮箱测试成功")
            else:
                QMessageBox.warning(self, "测试结果", f"❌ 邮箱 {email} 连接失败！\n\n错误: {result.get('error', '未知错误')}")
                self.status_label.setText("❌ 邮箱测试失败")

        except Exception as e:
            QMessageBox.critical(self, "测试错误", f"测试邮箱时发生异常:\n\n{str(e)}")
            self.status_label.setText("❌ 邮箱测试异常")


    
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin: 8px 0;
                padding-top: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 16px;
                padding: 0 8px 0 8px;
                color: #495057;
            }
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4caf50, stop:1 #388e3c);
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                border: none;
            }
            QPushButton:hover {
                background: #43a047;
            }
            QPushButton:disabled {
                background: #bdbdbd;
                color: #757575;
            }
            QLineEdit, QComboBox, QSpinBox {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 14px;
                background: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus {
                border-color: #4caf50;
                outline: none;
            }
        """)
    
    def create_mailbox(self):
        """创建邮箱"""
        name = self.name_edit.text().strip() or None
        domain = self.domain_combo.currentText()
        
        # 转换有效期格式
        duration_map = {
            "10分钟": "10m",
            "60分钟": "60m", 
            "2天": "2d",
            "7天": "7d"
        }
        duration = duration_map.get(self.duration_combo.currentText(), "10m")
        
        self.status_label.setText("正在创建邮箱...")
        self.create_btn.setEnabled(False)
        
        # 创建邮箱
        result = self.temp_mail_manager.create_mailbox(name, domain, duration)
        
        if not result.get("success"):
            QMessageBox.warning(self, "错误", f"创建邮箱失败: {result.get('error', '未知错误')}")
            self.status_label.setText("创建失败")
            self.create_btn.setEnabled(True)
    
    def on_email_set(self, result: dict):
        """邮箱设置成功回调"""
        email = result["email"]
        self.email_label.setText(f"📧 {email}")
        self.copy_btn.setEnabled(True)
        self.refresh_btn.setEnabled(True)
        self.get_code_btn.setEnabled(True)  # 启用获取验证码按钮
        self.delete_btn.setEnabled(True)  # 启用删除邮箱按钮

        # 启用清空邮件按钮（如果存在）
        if hasattr(self, 'clear_all_btn'):
            self.clear_all_btn.setEnabled(True)

        self.status_label.setText(result.get("message", f"邮箱设置成功: {email}"))

        # 清空邮件列表并加载现有邮件
        self.email_list.clear()
        self.email_detail.clear()

        # 加载现有邮件
        self.load_existing_messages()

    def on_monitoring_started(self, email: str):
        """监控开始回调"""
        # 显示简化的邮箱地址（只显示@前的部分）
        email_short = email.split('@')[0] if '@' in email else email
        if len(email_short) > 10:
            email_short = email_short[:10] + "..."

        self.monitor_status.setText(f"🔴 监控中: {email_short}")
        self.monitor_status.setStyleSheet("""
            color: #4caf50;
            font-weight: bold;
            font-size: 12px;
            padding: 4px 8px;
            background-color: #e8f5e8;
            border-radius: 4px;
            border: 1px solid #4caf50;
        """)
        self.status_label.setText(f"📡 开始监控: {email}")

    def on_monitoring_stopped(self):
        """监控停止回调"""
        self.monitor_status.setText("⏸️ 未监控")
        self.monitor_status.setStyleSheet("""
            color: #666;
            font-weight: bold;
            font-size: 12px;
            padding: 4px 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
            border: 1px solid #ddd;
        """)
        self.status_label.setText("⏹️ 监控已停止")

    def load_existing_messages(self):
        """加载现有邮件"""
        if not self.temp_mail_manager.current_email:
            return

        try:
            messages = self.temp_mail_manager.get_messages()
            self.log_message(f"📧 加载现有邮件: {len(messages)} 封")

            for message in messages:
                self.email_list.add_email(message)

            if messages:
                self.status_label.setText(f"📧 已加载 {len(messages)} 封邮件")
            else:
                self.status_label.setText("📭 暂无邮件")

        except Exception as e:
            self.log_message(f"❌ 加载邮件失败: {e}")
            self.status_label.setText("❌ 加载邮件失败")

    def set_email_and_monitor(self):
        """设置邮箱并开始监控"""
        # 如果正在初始化，跳过
        if hasattr(self, 'is_initializing') and self.is_initializing:
            logger.info("⏸️ 正在初始化，跳过邮箱设置")
            return

        current_index = self.email_combo.currentIndex()
        current_text = self.email_combo.currentText().strip()

        # 检查是否有有效的邮箱
        if current_index < 0 and not current_text:
            QMessageBox.warning(self, "错误", "请选择或输入邮箱地址")
            return

        # 获取当前选中的邮箱数据
        item_data = self.email_combo.itemData(current_index)
        if not item_data:
            # 如果是手动输入的邮箱
            email = self.email_combo.currentText().strip()
            if not is_valid_email(email):
                QMessageBox.warning(self, "错误", "请输入有效的邮箱地址格式")
                return
            pin = ""
        else:
            email = item_data.get('email', '')
            pin = item_data.get('pin', '')

        if not email:
            QMessageBox.warning(self, "错误", "邮箱地址不能为空")
            return

        # 立即更新界面状态
        self.status_label.setText("正在设置邮箱...")

        # 强制刷新界面
        QApplication.processEvents()

        # 使用QTimer延迟执行耗时操作，避免界面卡顿
        QTimer.singleShot(100, lambda: self._do_set_email_async(email, pin, item_data, current_index))

    def _do_set_email_async(self, email: str, pin: str, item_data: dict, current_index: int):
        """异步执行邮箱设置（简化版本，无进度条）"""
        try:
            # 简单的状态提示
            self.status_label.setText(f"🔄 正在设置邮箱: {email}")
            QApplication.processEvents()

            # 从邮箱地址中提取域名并更新API客户端
            if "@" in email:
                domain = email.split("@")[1]
                logger.info(f"🔄 从邮箱地址提取域名: {domain}")
                self.temp_mail_manager.update_domain(domain)

            # 设置邮箱（传递PIN码）
            result = self.temp_mail_manager.set_email(email, pin)

            if result.get("success"):
                # 开始监控
                self.temp_mail_manager.start_monitoring(email)

                # 如果是新输入的邮箱，添加到下拉框
                if not item_data:
                    display_text = f"{email}" + (f" (PIN: {pin})" if pin else "")
                    self.email_combo.setItemText(current_index, display_text)
                    self.email_combo.setItemData(current_index, {'email': email, 'pin': pin})

                # 保存配置
                self.save_config()

                self.status_label.setText("✅ 邮箱设置成功，监控已启动")
            else:
                QMessageBox.warning(self, "错误", f"设置邮箱失败: {result.get('error', '未知错误')}")
                self.status_label.setText("❌ 邮箱设置失败")

        except Exception as e:
            QMessageBox.warning(self, "错误", f"设置邮箱异常: {str(e)}")
            self.status_label.setText("❌ 邮箱设置异常")

    def auto_start_last_email(self):
        """自动启动上次使用的邮箱监控"""
        try:
            current_index = self.email_combo.currentIndex()
            if current_index >= 0:
                item_data = self.email_combo.itemData(current_index)
                if item_data and item_data.get('email'):
                    print(f"🔄 自动启动上次邮箱监控: {item_data.get('email')}")
                    self.set_email_and_monitor()
        except Exception as e:
            print(f"❌ 自动启动监控失败: {e}")

    def load_config_only(self):
        """仅加载配置，不自动启动监控（优化版本）"""
        try:
            if not os.path.exists(self.config_file):
                logger.info("📋 未找到配置文件，使用默认设置")
                return

            # 使用更高效的文件读取
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 批量处理邮箱列表，减少界面更新次数
            mailboxes = config.get('mailboxes', [])
            if not mailboxes:
                logger.info("📋 配置文件中没有邮箱")
                return

            # 暂时阻止信号发射，提高性能
            self.email_combo.blockSignals(True)

            try:
                # 批量添加邮箱项目
                items_to_add = []
                for mailbox in mailboxes:
                    if isinstance(mailbox, dict):
                        email = mailbox.get('email', '')
                        pin = mailbox.get('pin', '')
                        display_text = f"{email}" + (f" (PIN: {pin})" if pin else "")
                        items_to_add.append((display_text, mailbox))
                    else:
                        # 兼容旧格式（只有邮箱地址）
                        items_to_add.append((mailbox, {'email': mailbox, 'pin': ''}))

                # 一次性添加所有项目
                for display_text, data in items_to_add:
                    self.email_combo.addItem(display_text, data)

                # 设置上次使用的邮箱（但不自动启动）
                last_email = config.get('last_email')
                if last_email:
                    # 查找匹配的邮箱
                    for i in range(self.email_combo.count()):
                        item_data = self.email_combo.itemData(i)
                        if item_data and item_data.get('email') == last_email:
                            self.email_combo.setCurrentIndex(i)
                            break

            finally:
                # 恢复信号发射
                self.email_combo.blockSignals(False)

            logger.info(f"📋 已加载 {len(mailboxes)} 个邮箱配置")

        except Exception as e:
            logger.error(f"❌ 加载配置失败: {e}")
            # 确保信号恢复
            self.email_combo.blockSignals(False)

    def load_config(self):
        """加载配置（兼容旧方法，保持向后兼容）"""
        self.load_config_only()
        # 如果需要自动启动，可以在这里调用
        if hasattr(self, 'is_initializing') and not self.is_initializing:
            QTimer.singleShot(2000, self.auto_start_last_email)

    def save_config(self):
        """保存配置（带去重）"""
        try:
            mailboxes = []
            current_email = ""
            seen_emails = set()  # 用于去重

            for i in range(self.email_combo.count()):
                item_data = self.email_combo.itemData(i)
                if item_data:
                    email = item_data.get('email', '')
                    # 去重：只添加未见过的邮箱
                    if email and email not in seen_emails:
                        # 确保包含域名信息
                        mailbox_data = item_data.copy()
                        if 'domain' not in mailbox_data and '@' in email:
                            mailbox_data['domain'] = email.split('@')[1]
                        mailboxes.append(mailbox_data)
                        seen_emails.add(email)
                        if i == self.email_combo.currentIndex():
                            current_email = email

            config = {
                'mailboxes': mailboxes,
                'last_email': current_email
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            logger.info(f"💾 配置已保存: {len(mailboxes)} 个邮箱（已去重）")

            # 如果发现重复，重新加载下拉框
            if len(mailboxes) < self.email_combo.count():
                logger.warning(f"⚠️ 发现重复邮箱，已自动去重")
                self.load_config()

        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")

    def generate_random_email(self):
        """使用当前配置的域名生成真实邮箱地址"""
        try:
            import random

            # 获取当前正在使用的邮箱
            current_email = self.temp_mail_manager.get_current_email()

            if not current_email:
                QMessageBox.information(
                    self,
                    "提示",
                    "请先设置一个邮箱，然后才能基于当前域名生成新的邮箱地址。"
                )
                return

            # 从当前邮箱中提取域名
            current_domain = extract_domain_from_email(current_email)
            if not current_domain:
                QMessageBox.warning(
                    self,
                    "错误",
                    f"无法从邮箱地址 {current_email} 中提取有效的域名。"
                )
                return

            # 使用工具函数生成随机用户名（6-10位字母数字组合）
            username_length = random.randint(6, 10)
            username = generate_random_string(username_length, include_numbers=True, include_uppercase=False)

            # 生成完整的真实邮箱地址（使用当前域名）
            real_email = f"{username}@{current_domain}"

            # 更新显示区域
            self.generated_email_label.setText(real_email)
            self.generated_email_label.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    border-radius: 4px;
                    padding: 6px 12px;
                    color: #155724;
                    font-family: 'Consolas', 'Monaco', monospace;
                    font-weight: bold;
                }
            """)
            self.copy_generated_btn.setEnabled(True)

            # 检查邮箱是否已存在于下拉框中
            existing_index = -1
            for i in range(self.email_combo.count()):
                item_data = self.email_combo.itemData(i)
                if item_data and item_data.get('email') == real_email:
                    existing_index = i
                    break

            if existing_index >= 0:
                # 如果邮箱已存在，直接切换到该邮箱
                self.email_combo.setCurrentIndex(existing_index)
            else:
                # 如果邮箱不存在，添加新的邮箱项到下拉框
                display_text = real_email
                email_data = {
                    'email': real_email,
                    'pin': '',  # 生成的邮箱默认无PIN码
                    'domain': current_domain
                }

                # 暂时阻止信号，避免重复触发
                self.email_combo.blockSignals(True)
                try:
                    # 添加到下拉框
                    self.email_combo.addItem(display_text, email_data)
                    # 设置为当前选中项
                    self.email_combo.setCurrentIndex(self.email_combo.count() - 1)
                finally:
                    # 恢复信号
                    self.email_combo.blockSignals(False)

                # 手动触发切换事件，这会自动保存配置
                self.on_email_combo_changed(self.email_combo.currentIndex())

                # 确保立即保存配置
                QTimer.singleShot(200, self.save_config)

            # 显示成功消息
            self.status_label.setText(f"✅ 已生成并保存邮箱: {real_email}")

            logger.info(f"🎲 基于当前域名 {current_domain} 生成并保存邮箱: {real_email}")

            # 显示保存确认消息
            QTimer.singleShot(1000, lambda: self.show_save_confirmation(real_email))

        except Exception as e:
            logger.error(f"❌ 生成真实邮箱失败: {e}")
            QMessageBox.warning(self, "错误", f"生成真实邮箱失败: {str(e)}")

    def show_save_confirmation(self, email: str):
        """显示邮箱保存确认消息"""
        try:
            # 检查配置文件中是否确实保存了该邮箱
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    mailboxes = config.get('mailboxes', [])

                    # 检查邮箱是否在配置中
                    email_saved = any(mb.get('email') == email for mb in mailboxes)

                    if email_saved:
                        self.status_label.setText(f"💾 邮箱已持久化保存: {email}")
                        logger.info(f"✅ 确认邮箱已保存到配置文件: {email}")
                    else:
                        logger.warning(f"⚠️ 邮箱可能未正确保存: {email}")
                        # 如果没有保存，手动保存一次
                        self.save_config()
        except Exception as e:
            logger.error(f"❌ 检查邮箱保存状态失败: {e}")

    def copy_generated_email(self):
        """复制生成的邮箱地址到剪贴板"""
        try:
            email_text = self.generated_email_label.text()
            if email_text and email_text != "点击生成按钮创建新邮箱":
                clipboard = QApplication.clipboard()
                clipboard.setText(email_text)
                self.status_label.setText(f"✅ 已复制邮箱地址: {email_text}")
                logger.info(f"📋 已复制生成的邮箱地址: {email_text}")
            else:
                QMessageBox.information(self, "提示", "没有可复制的邮箱地址，请先生成邮箱。")
        except Exception as e:
            logger.error(f"❌ 复制邮箱地址失败: {e}")
            QMessageBox.warning(self, "错误", f"复制失败: {str(e)}")



    def extract_verification_codes(self):
        """从当前选中邮件中提取6位数字验证码"""
        try:
            # 获取当前选中的邮件
            current_item = self.email_list.currentItem()
            if not current_item:
                QMessageBox.information(self, "提示", "请先选择一封邮件")
                return

            email_data = current_item.data(Qt.UserRole)
            if not email_data:
                QMessageBox.information(self, "提示", "无法获取邮件数据")
                return

            # 获取邮件详细内容
            mail_id = email_data.get('id')
            if not mail_id:
                QMessageBox.information(self, "提示", "邮件ID无效")
                return

            # 通过API获取邮件的文本内容
            try:
                detail = self.temp_mail_manager.api.get_message_detail(
                    email=self.temp_mail_manager.current_email,
                    mail_id=mail_id
                )
                if not detail:
                    QMessageBox.information(self, "提示", "无法获取邮件详情")
                    return

                # 只使用文本内容
                text_content = detail.get('text_content', '')
                if not text_content:
                    QMessageBox.information(self, "提示", "邮件没有文本内容")
                    return

            except Exception as e:
                logger.error(f"获取邮件详情失败: {e}")
                QMessageBox.warning(self, "错误", f"获取邮件详情失败: {str(e)}")
                return

            # 使用工具函数提取验证码
            found_codes = extract_verification_codes(text_content)

            if found_codes:
                # 显示找到的验证码
                subject = email_data.get('subject', '无主题')
                sender = email_data.get('from', '未知发件人')

                if len(found_codes) == 1:
                    # 只有一个验证码，直接复制并提示
                    code = found_codes[0]
                    clipboard = QApplication.clipboard()
                    clipboard.setText(code)

                    QMessageBox.information(
                        self,
                        "🔑 找到验证码",
                        f"从邮件中提取到6位数字验证码：\n\n"
                        f"验证码：{code}\n"
                        f"发件人：{sender}\n"
                        f"主题：{subject}\n\n"
                        f"验证码已自动复制到剪贴板！"
                    )
                    self.status_label.setText(f"✅ 已复制验证码: {code}")
                else:
                    # 多个验证码，让用户选择
                    codes_text = '\n'.join([f"• {code}" for code in found_codes])

                    msg = QMessageBox(self)
                    msg.setWindowTitle("🔑 找到多个验证码")
                    msg.setText(f"从邮件中提取到多个6位数字验证码：\n\n{codes_text}\n\n发件人：{sender}")

                    copy_first_btn = msg.addButton("复制第一个", QMessageBox.AcceptRole)
                    copy_all_btn = msg.addButton("复制所有", QMessageBox.ActionRole)
                    msg.addButton("取消", QMessageBox.RejectRole)

                    result = msg.exec()

                    clipboard = QApplication.clipboard()
                    if result == QMessageBox.AcceptRole:
                        clipboard.setText(found_codes[0])
                        self.status_label.setText(f"✅ 已复制验证码: {found_codes[0]}")
                    elif result == QMessageBox.ActionRole:
                        all_codes = ', '.join(found_codes)
                        clipboard.setText(all_codes)
                        self.status_label.setText(f"✅ 已复制所有验证码: {all_codes}")

                logger.info(f"🔑 从邮件 {mail_id} 提取到 {len(found_codes)} 个6位数字验证码: {found_codes}")

            else:
                QMessageBox.information(
                    self,
                    "提示",
                    "未在当前邮件中找到6位数字验证码\n\n"
                    "请确保邮件包含6位数字验证码（如：123456）"
                )
                self.status_label.setText("❌ 未找到6位数字验证码")



        except Exception as e:
            logger.error(f"❌ 提取验证码失败: {e}")
            QMessageBox.warning(self, "错误", f"提取验证码失败: {str(e)}")

    def copy_email(self):
        """复制邮箱地址"""
        email = self.temp_mail_manager.get_current_email()
        if email:
            clipboard = QApplication.clipboard()
            clipboard.setText(email)
            self.status_label.setText("邮箱地址已复制到剪贴板")
    
    def refresh_messages(self):
        """刷新邮件（异步优化）"""
        if not self.temp_mail_manager.current_email:
            return

        self.status_label.setText("正在刷新邮件...")

        # 立即清空列表，给用户反馈
        self.email_list.clear()

        # 强制刷新界面
        QApplication.processEvents()

        # 异步加载邮件，避免阻塞UI
        QTimer.singleShot(50, self._refresh_messages_async)

    def _refresh_messages_async(self):
        """异步刷新邮件"""
        try:
            self.load_existing_messages()
            self.status_label.setText("✅ 邮件刷新完成")
        except Exception as e:
            logger.error(f"❌ 刷新邮件异常: {e}")
            self.status_label.setText("❌ 邮件刷新失败")
    
    def on_new_message(self, message: dict):
        """收到新邮件回调"""
        # 添加到邮件列表
        self.email_list.add_email(message)

        # 获取邮件信息
        subject = message.get('subject', '无主题')
        sender = message.get('from', '未知发件人')

        # 更新状态
        self.status_label.setText(f"📧 收到新邮件: {subject}")

        # 显示新邮件弹窗提醒
        self.show_new_mail_notification(subject, sender)

    def show_new_mail_notification(self, subject: str, sender: str):
        """显示新邮件弹窗通知"""
        try:
            # 创建信息弹窗（不设置图标，避免Windows图标错误）
            msg = QMessageBox(self)
            msg.setWindowTitle("📧 新邮件提醒")
            # 移除 setIcon 调用，避免 qt_imageToWinHBITMAP 错误

            # 设置消息内容
            message_text = f"收到新邮件！\n\n📧 主题: {subject}\n👤 发件人: {sender}"
            msg.setText(message_text)

            # 添加按钮
            view_btn = msg.addButton("查看邮件", QMessageBox.AcceptRole)
            msg.addButton("稍后查看", QMessageBox.RejectRole)

            # 设置样式
            msg.setStyleSheet("""
                QMessageBox {
                    background-color: #f8f9fa;
                }
                QMessageBox QLabel {
                    color: #333;
                    font-size: 14px;
                    padding: 10px;
                }
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-size: 12px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                }
            """)

            # 显示弹窗并处理用户选择
            msg.exec()

            # 如果用户点击了"查看邮件"
            if msg.clickedButton() == view_btn:
                self.view_latest_email(subject)

        except Exception as e:
            self.log_message(f"❌ 显示新邮件通知失败: {e}")

    def view_latest_email(self, subject: str):
        """查看最新邮件（根据主题查找并选中）"""
        try:
            # 在邮件列表中查找匹配的邮件
            for i in range(self.email_list.count()):
                item = self.email_list.item(i)
                if item:
                    email_data = item.data(Qt.UserRole)
                    if email_data and email_data.get('subject') == subject:
                        # 选中并显示这封邮件
                        self.email_list.setCurrentItem(item)
                        self.show_email_detail(item)
                        self.log_message(f"📧 已定位到邮件: {subject}")
                        return

            # 如果没找到，显示提示
            self.log_message(f"❌ 未找到邮件: {subject}")

        except Exception as e:
            self.log_message(f"❌ 查看邮件失败: {e}")

    def clear_log(self):
        """清空日志"""
        self.log_entries.clear()
        self.log_widget.clear()
        self.log_message("📋 日志已清空")

    def log_message(self, message: str):
        """在GUI中显示日志消息（兼容旧接口）"""
        self.log_message_with_level(message, LogLevel.INFO)

    def log_message_with_level(self, message: str, level: str = LogLevel.INFO):
        """在GUI中显示带级别的日志消息"""
        try:
            # 获取当前时间（更详细的时间戳）
            from datetime import datetime
            now = datetime.now()
            timestamp = now.strftime("%H:%M:%S.%f")[:-3]  # 包含毫秒
            date_str = now.strftime("%m-%d")  # 月-日

            # 根据日志级别设置颜色和图标
            level_info = self._get_level_info(level)
            icon = level_info["icon"]
            color = level_info["color"]
            level_name = level_info["name"]

            # 创建更详细的日志条目对象
            log_entry = {
                "timestamp": timestamp,
                "date": date_str,
                "level": level,
                "message": message,
                "formatted": f'<span style="color: {color}; font-family: Consolas, monospace;">'
                           f'<b>[{date_str} {timestamp}]</b> '
                           f'<span style="background-color: {level_info["bg_color"]}; padding: 1px 4px; border-radius: 2px; color: white; font-size: 10px;">{level_name}</span> '
                           f'{icon} {message}'
                           f'</span>'
            }

            # 添加到日志条目列表
            self.log_entries.append(log_entry)

            # 限制日志条目数量（保留最近200条）
            if len(self.log_entries) > 200:
                self.log_entries = self.log_entries[-200:]

            # 根据当前过滤级别决定是否显示
            if self._should_show_log(level):
                self.log_widget.append(log_entry["formatted"])

                # 自动滚动到底部
                scrollbar = self.log_widget.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())

        except Exception as e:
            # 如果日志系统出错，回退到控制台
            print(f"[LOG ERROR] {message}")
            print(f"[LOG ERROR] 日志系统错误: {e}")

    def _get_level_info(self, level: str) -> dict:
        """获取日志级别的显示信息"""
        level_map = {
            LogLevel.DEBUG: {
                "icon": "🔍",
                "color": "#6c757d",
                "bg_color": "#6c757d",
                "name": "DEBUG"
            },
            LogLevel.INFO: {
                "icon": "ℹ️",
                "color": "#17a2b8",
                "bg_color": "#17a2b8",
                "name": "INFO"
            },
            LogLevel.WARNING: {
                "icon": "⚠️",
                "color": "#ffc107",
                "bg_color": "#ffc107",
                "name": "WARN"
            },
            LogLevel.ERROR: {
                "icon": "❌",
                "color": "#dc3545",
                "bg_color": "#dc3545",
                "name": "ERROR"
            }
        }
        return level_map.get(level, {
            "icon": "📝",
            "color": "#495057",
            "bg_color": "#495057",
            "name": "LOG"
        })

    def _should_show_log(self, level: str) -> bool:
        """根据当前过滤级别判断是否应该显示日志"""
        level_priority = {
            LogLevel.DEBUG: 0,
            LogLevel.INFO: 1,
            LogLevel.WARNING: 2,
            LogLevel.ERROR: 3
        }

        filter_priority = {
            "全部": -1,
            "调试": 0,
            "信息": 1,
            "警告": 2,
            "错误": 3
        }

        current_filter_priority = filter_priority.get(self.current_log_filter, 1)
        log_level_priority = level_priority.get(level, 1)

        return current_filter_priority == -1 or log_level_priority >= current_filter_priority

    def on_log_level_changed(self, level_text: str):
        """日志级别过滤器改变时的处理"""
        self.current_log_filter = level_text
        self._refresh_log_display()

    def _refresh_log_display(self):
        """根据当前过滤级别刷新日志显示"""
        self.log_widget.clear()

        for entry in self.log_entries:
            if self._should_show_log(entry["level"]):
                self.log_widget.append(entry["formatted"])

        # 自动滚动到底部
        scrollbar = self.log_widget.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def save_log(self):
        """保存日志到文件"""
        try:
            from datetime import datetime
            from PySide6.QtWidgets import QFileDialog

            # 生成安全的默认文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = sanitize_filename(f"tempmail_log_{timestamp}.txt")

            # 打开文件保存对话框
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "保存日志文件",
                default_filename,
                "文本文件 (*.txt);;所有文件 (*.*)"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"临时邮箱管理工具 - 运行日志\n")
                    f.write(f"导出时间: {get_current_timestamp()}\n")
                    f.write(f"日志级别过滤: {self.current_log_filter}\n")
                    f.write("=" * 50 + "\n\n")

                    # 写入符合当前过滤条件的日志
                    for entry in self.log_entries:
                        if self._should_show_log(entry["level"]):
                            # 创建纯文本格式的日志条目
                            level_info = self._get_level_info(entry["level"])
                            clean_text = f'[{entry["date"]} {entry["timestamp"]}] [{level_info["name"]}] {entry["message"]}'
                            f.write(clean_text + "\n")

                self.log_message(f"📁 日志已保存到: {filename}")

        except Exception as e:
            self.log_message(f"❌ 保存日志失败: {e}")

    def show_email_detail(self, item: QListWidgetItem):
        """显示邮件详情（优化流畅度）"""
        try:
            # 立即显示加载状态
            self.status_label.setText("🔄 正在加载邮件详情...")
            QApplication.processEvents()

            email_data = item.data(Qt.UserRole)

            # 异步加载邮件详情，避免阻塞UI
            QTimer.singleShot(5, lambda: self._load_email_detail_async(email_data))

        except Exception as e:
            logger.error(f"❌ 显示邮件详情失败: {e}")
            self.status_label.setText("❌ 加载邮件详情失败")

    def _load_email_detail_async(self, email_data):
        """异步加载邮件详情"""
        try:
            # 显示邮件详情
            self.email_detail.show_email(email_data, self.temp_mail_manager)

            # 更新状态
            self.status_label.setText("✅ 邮件详情加载完成")

        except Exception as e:
            logger.error(f"❌ 异步加载邮件详情失败: {e}")
            self.status_label.setText("❌ 邮件详情加载失败")
    
    def delete_mailbox(self):
        """删除邮箱"""
        reply = QMessageBox.question(
            self, "确认删除", 
            "确定要删除当前邮箱吗？所有邮件将被永久删除。",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.temp_mail_manager.delete_current_mailbox()
    
    def stop_monitoring(self):
        """停止监控"""
        self.temp_mail_manager.stop_monitoring()
        self.email_label.setText("暂无监控")
        self.copy_btn.setEnabled(False)
        self.refresh_btn.setEnabled(False)
        self.stop_btn.setEnabled(False)
        self.start_btn.setEnabled(True)
        self.email_list.clear()
        self.email_detail.clear()
        self.status_label.setText("监控已停止")

    def start_monitoring(self):
        """开始监控"""
        email = self.email_edit.text().strip()
        if not email:
            QMessageBox.warning(self, "错误", "请输入邮箱地址")
            return

        if "@" not in email:
            QMessageBox.warning(self, "错误", "请输入有效的邮箱地址")
            return

        self.status_label.setText("正在设置邮箱...")
        self.start_btn.setEnabled(False)

        # 设置邮箱并开始监控
        result = self.temp_mail_manager.set_email(email)

        if not result.get("success"):
            QMessageBox.warning(self, "错误", f"设置邮箱失败: {result.get('error', '未知错误')}")
            self.start_btn.setEnabled(True)
    

