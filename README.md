# 临时邮箱管理工具

一个基于 PySide6 的临时邮箱管理桌面应用程序，集成 TempMail.Plus 服务。

## 功能特性

### 📧 临时邮箱管理
- **一键创建**: 快速创建临时邮箱地址
- **多域名支持**: 支持多个邮箱域名选择
- **实时监控**: 自动监控新邮件到达
- **邮件查看**: 完整的邮件内容显示
- **自动刷新**: 可配置的自动邮件检查
- **邮箱管理**: 复制地址、删除邮箱等便捷操作

## 安装要求

- Python 3.8+
- PySide6 6.5.0+
- requests 2.28.0+

## 快速开始

### 方式一：直接运行
```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
python run_app.py
```

### 方式二：演示模式
```bash
# 启动演示程序
python demo.py

# 或者直接启动GUI
python demo.py --gui

# 或者测试API功能
python demo.py --api
```

### 方式三：测试功能
```bash
# 运行完整测试
python test_tempmail.py
```

## 使用说明

### 临时邮箱功能

1. **创建邮箱**:
   - 输入邮箱名称（可留空自动生成）
   - 选择邮箱域名
   - 设置有效期（10分钟到7天）
   - 点击"创建邮箱"

2. **管理邮箱**:
   - 复制邮箱地址到剪贴板
   - 手动刷新邮件列表
   - 启用自动刷新功能
   - 删除当前邮箱

3. **查看邮件**:
   - 在左侧邮件列表中选择邮件
   - 右侧会显示完整的邮件内容
   - 支持 HTML 格式邮件显示

## 项目结构

```
augment/
├── main.py                 # 主程序入口
├── run_app.py             # 启动脚本
├── requirements.txt        # 依赖包列表
├── test_tempmail.py       # 临时邮箱测试脚本
├── README.md              # 项目说明文档
└── app/
    ├── tempmail_manager.py # 临时邮箱API管理
    └── tempmail_widget.py  # 临时邮箱GUI界面
```

## API 端点说明

### TempMail.Plus API

基于对 tempmail.plus 网站的分析，主要端点包括：

- **创建邮箱**: `POST /api/create`
- **获取邮件**: `GET /api/messages`
- **删除邮箱**: `DELETE /api/delete`

**注意**: 实际的 API 端点可能需要通过浏览器开发者工具进一步分析确认。当前实现包含了模拟数据用于测试。

### 支持的邮箱域名

- mailto.plus
- fexpost.com
- fexbox.org
- mailbox.in.ua
- rover.info
- chitthi.in
- fextemp.com
- any.pink
- merepost.com

## 测试功能

运行测试脚本来验证功能：

```bash
# API 功能测试
python test_tempmail.py

# GUI 界面测试
python test_tempmail.py --gui
```

## 注意事项

1. **网络连接**: 确保网络连接正常
2. **临时邮箱限制**: 临时邮箱有时效性，请及时查看重要邮件
3. **隐私保护**: 不要在临时邮箱中接收敏感信息

## 故障排除

### 常见问题

1. **临时邮箱创建失败**
   - 检查网络连接
   - 尝试更换不同的域名
   - 确认 TempMail.Plus 服务可用

2. **邮件接收延迟**
   - 启用自动刷新功能
   - 手动点击刷新按钮
   - 检查网络连接稳定性

3. **界面显示异常**
   - 确保已安装 PySide6
   - 检查系统字体支持

## 开发说明

### 扩展 API 支持

要添加对其他临时邮箱服务的支持：

1. 在 `tempmail_manager.py` 中添加新的 API 类
2. 实现相应的创建、获取、删除方法
3. 在 `tempmail_widget.py` 中添加服务选择选项

### 自定义样式

程序使用 Qt 样式表，可以在各个组件的 `setStyleSheet()` 方法中修改界面样式。

## 许可证

本项目仅供学习和研究使用。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。
