#!/usr/bin/env python3
"""
测试不同邮箱后缀的生成和持久化功能
"""

import json
import os
from app.utils import generate_random_string

def test_suffix_generation():
    """测试使用不同邮箱后缀生成邮箱"""
    print("🧪 测试不同邮箱后缀的生成功能...")
    
    # 可用的邮箱后缀列表
    available_suffixes = [
        "mailto.plus",
        "fexpost.com", 
        "fexbox.org",
        "mailbox.in.ua",
        "rover.info",
        "chitthi.in",
        "fextemp.com",
        "any.pink",
        "merepost.com"
    ]
    
    print(f"\n📋 可用邮箱后缀 ({len(available_suffixes)} 个):")
    for i, suffix in enumerate(available_suffixes, 1):
        print(f"  {i}. {suffix}")
    
    # 测试生成不同后缀的邮箱
    print(f"\n🎲 测试生成不同后缀的邮箱:")
    generated_emails = []
    
    for suffix in available_suffixes[:5]:  # 测试前5个后缀
        # 生成随机用户名
        username = generate_random_string(8, include_numbers=True, include_uppercase=False)
        email = f"{username}@{suffix}"
        generated_emails.append({
            'email': email,
            'suffix': suffix,
            'username': username
        })
        
        print(f"  📧 {suffix} -> {email}")
    
    # 测试保存到配置文件
    print(f"\n💾 测试保存到配置文件:")
    config_file = "tempmail_config.json"
    
    # 备份原配置
    backup_file = "tempmail_config_backup_suffix.json"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            original_config = json.load(f)
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(original_config, f, ensure_ascii=False, indent=2)
        print(f"  ✅ 已备份原配置到 {backup_file}")
        config = original_config.copy()
    else:
        config = {'mailboxes': [], 'last_email': ''}
        print(f"  📋 创建新配置文件")
    
    # 添加生成的邮箱到配置
    original_count = len(config.get('mailboxes', []))
    existing_emails = [mb.get('email') for mb in config.get('mailboxes', [])]
    
    added_count = 0
    for email_info in generated_emails:
        email = email_info['email']
        suffix = email_info['suffix']
        
        if email not in existing_emails:
            new_mailbox = {
                'email': email,
                'pin': '',
                'domain': suffix  # 这里domain字段实际存储的是邮箱后缀
            }
            config['mailboxes'].append(new_mailbox)
            added_count += 1
            print(f"    ➕ 添加: {email}")
        else:
            print(f"    ⚠️  已存在: {email}")
    
    # 设置最后一个生成的邮箱为当前邮箱
    if generated_emails:
        config['last_email'] = generated_emails[-1]['email']
    
    # 保存配置
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 保存结果:")
    print(f"  - 原有邮箱: {original_count} 个")
    print(f"  - 新增邮箱: {added_count} 个")
    print(f"  - 总计邮箱: {len(config['mailboxes'])} 个")
    print(f"  - 当前邮箱: {config.get('last_email', '无')}")
    
    # 验证保存结果
    print(f"\n✅ 验证保存结果:")
    with open(config_file, 'r', encoding='utf-8') as f:
        saved_config = json.load(f)
    
    print(f"  📋 配置文件中的邮箱:")
    for i, mailbox in enumerate(saved_config.get('mailboxes', []), 1):
        email = mailbox.get('email', '未知')
        suffix = mailbox.get('domain', '未知')
        pin = mailbox.get('pin', '')
        print(f"    {i}. {email} (后缀: {suffix}, PIN: {pin or '无'})")
    
    # 询问是否恢复原配置
    print(f"\n🔄 测试完成！")
    if os.path.exists(backup_file):
        restore = input("是否恢复原配置？(y/N): ").strip().lower()
        if restore == 'y':
            with open(backup_file, 'r', encoding='utf-8') as f:
                original_config = json.load(f)
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(original_config, f, ensure_ascii=False, indent=2)
            print("✅ 已恢复原配置")
            os.remove(backup_file)
            print("🗑️  已删除备份文件")
        else:
            print("📝 保留测试结果，备份文件已保存")

if __name__ == "__main__":
    test_suffix_generation()
