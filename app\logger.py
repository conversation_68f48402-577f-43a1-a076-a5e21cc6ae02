from PySide6.QtCore import QObject, Signal


class LogLevel:
    """日志级别常量"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


class Logger(QObject):
    """统一日志管理器"""
    log_message = Signal(str, str)  # (message, level)

    def __init__(self):
        super().__init__()
        self._enabled = True

    def debug(self, message: str):
        """调试日志"""
        if self._enabled:
            self.log_message.emit(message, LogLevel.DEBUG)

    def info(self, message: str):
        """信息日志"""
        if self._enabled:
            self.log_message.emit(message, LogLevel.INFO)

    def warning(self, message: str):
        """警告日志"""
        if self._enabled:
            self.log_message.emit(message, LogLevel.WARNING)

    def error(self, message: str):
        """错误日志"""
        if self._enabled:
            self.log_message.emit(message, LogLevel.ERROR)

    def set_enabled(self, enabled: bool):
        """启用/禁用日志"""
        self._enabled = enabled


# 全局日志实例
logger = Logger()
