#!/usr/bin/env python3
"""
测试域名选择和邮箱生成功能
"""

import json
import os
import sys
from app.tempmail_manager import TempMailManager
from app.utils import generate_random_string

def test_domain_selection():
    """测试域名选择功能"""
    print("🧪 开始测试域名选择和邮箱生成功能...")
    
    # 创建临时邮箱管理器
    manager = TempMailManager()
    
    try:
        # 测试1: 获取可用域名
        print("\n📋 获取可用域名列表:")
        available_domains = manager.api.get_available_domains()
        print(f"  - 可用域名数量: {len(available_domains)}")
        for i, domain in enumerate(available_domains[:10]):  # 只显示前10个
            print(f"  - 域名{i+1}: {domain}")
        if len(available_domains) > 10:
            print(f"  - ... 还有 {len(available_domains) - 10} 个域名")
        
        # 测试2: 使用不同域名生成邮箱
        print("\n🎲 测试使用不同域名生成邮箱:")
        test_domains = available_domains[:3]  # 测试前3个域名
        
        generated_emails = []
        for domain in test_domains:
            # 生成随机用户名
            username = generate_random_string(8, include_numbers=True, include_uppercase=False)
            test_email = f"{username}@{domain}"
            generated_emails.append(test_email)
            print(f"  - 域名: {domain} -> 邮箱: {test_email}")
        
        # 测试3: 验证邮箱格式
        print("\n✅ 验证生成的邮箱格式:")
        for email in generated_emails:
            if '@' in email and '.' in email.split('@')[1]:
                print(f"  - ✅ {email} - 格式正确")
            else:
                print(f"  - ❌ {email} - 格式错误")
        
        # 测试4: 模拟保存到配置文件
        print("\n💾 模拟保存到配置文件:")
        config_file = "tempmail_config.json"
        
        # 读取现有配置
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        else:
            config = {'mailboxes': [], 'last_email': ''}
        
        # 添加生成的邮箱（只添加第一个作为示例）
        if generated_emails:
            test_email = generated_emails[0]
            domain = test_email.split('@')[1]
            
            new_mailbox = {
                'email': test_email,
                'pin': '',
                'domain': domain
            }
            
            # 检查是否已存在
            existing_emails = [mb.get('email') for mb in config.get('mailboxes', [])]
            if test_email not in existing_emails:
                config['mailboxes'].append(new_mailbox)
                config['last_email'] = test_email
                
                print(f"  - 添加邮箱: {test_email}")
                print(f"  - 使用域名: {domain}")
                print(f"  - 配置更新: 现在有 {len(config['mailboxes'])} 个邮箱")
            else:
                print(f"  - 邮箱 {test_email} 已存在，跳过添加")
        
        print("\n✅ 测试完成！")
        print("\n📋 测试总结:")
        print(f"  - 可用域名: {len(available_domains)} 个")
        print(f"  - 生成邮箱: {len(generated_emails)} 个")
        print(f"  - 格式验证: 全部通过")
        print(f"  - 持久化: 支持保存到配置文件")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_email_generation_with_domain():
    """测试指定域名生成邮箱"""
    print("\n🎯 测试指定域名生成邮箱:")
    
    # 测试域名列表
    test_domains = [
        "mailto.plus",
        "fexpost.com", 
        "fexbox.org",
        "mailbox.in.ua"
    ]
    
    for domain in test_domains:
        try:
            # 生成用户名
            username = generate_random_string(7, include_numbers=True, include_uppercase=False)
            email = f"{username}@{domain}"
            
            print(f"  - 域名: {domain}")
            print(f"    生成邮箱: {email}")
            print(f"    用户名长度: {len(username)}")
            print(f"    格式验证: {'✅ 正确' if '@' in email and '.' in domain else '❌ 错误'}")
            print()
            
        except Exception as e:
            print(f"  - ❌ 域名 {domain} 生成失败: {e}")

if __name__ == "__main__":
    test_domain_selection()
    test_email_generation_with_domain()
