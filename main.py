import sys
from PySide6.QtWidgets import QApplication, QWidget, QLabel, QVBoxLayout, QGraphicsDropShadowEffect
from PySide6.QtGui import QFont, QColor
from PySide6.QtCore import Qt, QTimer
from app.tempmail_widget import TempMailWidget

class MainWindow(QWidget):
    """临时邮箱管理主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("临时邮箱管理工具")
        self.resize(1200, 800)

        # 快速初始化基础界面
        self._init_ui_fast()
        self.center()

        # 延迟加载复杂样式，避免启动卡顿
        QTimer.singleShot(200, self._load_fancy_styles)

    def center(self):
        """窗口居中显示"""
        qr = self.frameGeometry()
        cp = self.screen().availableGeometry().center()
        qr.moveCenter(cp)
        self.move(qr.topLeft())

    def _init_ui_fast(self):
        """快速初始化用户界面（无复杂效果）"""
        layout = QVBoxLayout()

        # 主标题（简化版本）
        self.title_label = QLabel("📧 临时邮箱管理工具", self)
        self.title_label.setObjectName("titleLabel")

        # 创建字体
        title_font = QFont("Microsoft YaHei, SimHei, Arial, sans-serif", 24)
        title_font.setBold(True)
        self.title_label.setFont(title_font)

        self.title_label.setAlignment(Qt.AlignCenter)

        # 设置简单样式
        self.title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 12px 0;
                background-color: #ecf0f1;
                border-radius: 6px;
                margin: 5px;
            }
        """)

        layout.addWidget(self.title_label)

        # 临时邮箱组件
        self.temp_mail_widget = TempMailWidget()
        layout.addWidget(self.temp_mail_widget)

        self.setLayout(layout)

    def _load_fancy_styles(self):
        """加载复杂的视觉效果"""
        try:
            # 设置背景渐变
            self.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #f0f4fa, stop:1 #e3eaf6);
                }
                QLabel#titleLabel {
                    color: #222;
                    font-size: 28px;
                    font-weight: bold;
                    letter-spacing: 2px;
                    padding: 16px 0 8px 0;
                    background: transparent;
                }
            """)

            # 添加阴影效果
            shadow = QGraphicsDropShadowEffect(self)
            shadow.setBlurRadius(15)
            shadow.setColor(QColor(100, 100, 100, 150))
            shadow.setOffset(0, 3)
            self.title_label.setGraphicsEffect(shadow)

            # 更新字体大小
            font = self.title_label.font()
            font.setPointSize(28)
            self.title_label.setFont(font)

        except Exception as e:
            print(f"加载复杂样式失败: {e}")

if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 设置应用程序属性，避免图标相关错误
    app.setApplicationName("临时邮箱管理工具")
    app.setApplicationVersion("1.0.0")

    window = MainWindow()
    window.show()
    sys.exit(app.exec())