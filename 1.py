#!/usr/bin/env python3
import requests
import time
import json
import os
import re
import getpass
import random
import hashlib
import threading
import queue
from tqdm import tqdm
from urllib.parse import urljoin

# ==============================================================================
# --- 全局配置 ---
# ==============================================================================

# --- GitHub API 搜索配置 (源自 searchstr.py) ---
GITHUB_API_URL = "https://api.github.com/"
SEARCH_CODE_ENDPOINT = "search/code"
SEARCH_REPOS_ENDPOINT = "search/repositories"
BASE_QUERY = "AIzaSy"  # Gemini API 密钥的通用前缀
TARGET_LENGTH = 39     # Gemini API 密钥的标准长度
KEY_PATTERN = re.compile(f'({BASE_QUERY}[A-Za-z0-9_-]{{{TARGET_LENGTH - len(BASE_QUERY)}}})')
# 用于构建更具体搜索查询的字符集，以绕过1000个结果的限制
# 可以扩展此集合以进行更广泛的搜索，例如："ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-"
CHAR_SET = "WXYZ" 
CACHE_DIR = "ghapi_cache" # 缓存GitHub API响应的目录

# --- 密钥验证配置 (源自 check.py) ---
VALID_KEYS_FILENAME = "valid_keys.txt"
PROGRESS_CACHE_FILENAME = "checked_keys.cache"
NUM_THREADS = 10  # 增加线程数以加快验证速度
MODEL_NAME = "gemini-1.5-flash-latest" # 使用成本较低、响应较快的模型进行测试
API_ENDPOINT_TEMPLATE = f"https://generativelanguage.googleapis.com/v1beta/models/{MODEL_NAME}:generateContent"
HEADERS = {'Content-Type': 'application/json'}
TEST_PAYLOAD = json.dumps({"contents": [{"parts":[{"text": "Hi"}]}]}) # 一个非常简单的测试负载，减少消耗

# --- 网络和延迟配置 ---
REQUEST_TIMEOUT = 10  # API请求超时时间（秒）
# 验证每个密钥之间的延迟（秒），这是单个线程的延迟
DELAY_BETWEEN_REQUESTS = 0.5 
# GitHub API 请求的重试和冷却配置
MAX_RETRIES = 3
COOLDOWN_MIN_SECONDS = 15
COOLDOWN_MAX_SECONDS = 30
REQUEST_DELAY_MIN_SECONDS = 1
REQUEST_DELAY_MAX_SECONDS = 3

# --- 界面与输出配置 ---
# ANSI 颜色代码
COLOR_GREEN = "\033[92m"
COLOR_RED = "\033[91m"
COLOR_YELLOW = "\033[93m"
COLOR_RESET = "\033[0m"

# ==============================================================================
# --- 全局线程锁 ---
# ==============================================================================
print_lock = threading.Lock()
file_lock = threading.Lock()

# ==============================================================================
# --- GitHub 搜索功能 (源自 searchstr.py) ---
# ==============================================================================

def get_github_token():
    """安全地从用户处获取 GitHub 令牌。"""
    print(f"{COLOR_YELLOW}为了获得更高的API请求速率，强烈建议使用GitHub个人访问令牌。{COLOR_RESET}")
    print("您的令牌不会被保存，仅用于本次会话。")
    token = getpass.getpass("请输入您的GitHub Personal Access Token (留空则匿名访问): ")
    if not token:
        print(f"{COLOR_YELLOW}警告：未提供令牌。将以未经身份验证的方式请求，速率限制极低。{COLOR_RESET}")
    return token

def sanitize_filename(name):
    """清理查询字符串以用作安全的文件名。"""
    return re.sub(r'[\s":*?<>|/\\]', '_', name)

def make_request_with_retry(url, headers, params):
    """带有重试和冷却逻辑的GitHub API请求函数。"""
    for attempt in range(MAX_RETRIES):
        try:
            response = requests.get(url, headers=headers, params=params, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            if attempt < MAX_RETRIES - 1:
                tqdm.write(f"\n  {COLOR_YELLOW}请求失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {e}{COLOR_RESET}")
                cooldown = random.uniform(COOLDOWN_MIN_SECONDS, COOLDOWN_MAX_SECONDS)
                tqdm.write(f"  将冷却 {cooldown:.2f} 秒后重试...")
                time.sleep(cooldown)
            else:
                tqdm.write(f"\n  {COLOR_RED}请求失败：已达到最大重试次数。放弃查询 {params.get('q')}{COLOR_RESET}")
                return None

def fetch_all_pages_for_query(endpoint_url, query, headers, description_prefix):
    """为单个精确查询获取所有分页结果。"""
    items = []
    params = {'q': query, 'per_page': 100, 'page': 1}
    
    response = make_request_with_retry(endpoint_url, headers, params)
    if not response:
        return items

    data = response.json()
    items.extend(data.get('items', []))
    
    total_count = data.get('total_count', 0)
    if total_count > 1000:
        tqdm.write(f"\n{COLOR_YELLOW}警告: 查询 '{query}' 找到 {total_count} 个结果, GitHub API仍将限制为前1000个。{COLOR_RESET}")
        total_count = 1000
        
    total_pages = (total_count + params['per_page'] - 1) // params['per_page']

    if total_pages > 1:
        with tqdm(total=total_pages, desc=f"{description_prefix} '{query}'", leave=False, unit="页") as pbar:
            pbar.update(1)
            for page in range(2, total_pages + 1):
                time.sleep(random.uniform(REQUEST_DELAY_MIN_SECONDS, REQUEST_DELAY_MAX_SECONDS))
                params['page'] = page
                response = make_request_with_retry(endpoint_url, headers, params)
                
                if response:
                    paged_data = response.json()
                    items.extend(paged_data.get('items', []))
                    pbar.update(1)
                else:
                    tqdm.write(f"\n{COLOR_RED}在获取查询 '{query}' 的第 {page} 页时失败，此查询的结果将不完整且不会被缓存。{COLOR_RESET}")
                    return [] # 返回空列表表示此查询失败，避免缓存不完整的数据
    return items

def extract_strings_from_results(results_data, found_set):
    """从API结果数据中提取符合条件的字符串。"""
    if not results_data:
        return
        
    for item in results_data:
        search_text = ""
        if 'text_matches' in item:
            search_text = " ".join([match.get('fragment', '') for match in item.get('text_matches', [])])
        elif 'name' in item:
             search_text = item.get('name', '')

        if search_text:
            matches = KEY_PATTERN.findall(search_text)
            for match in matches:
                found_set.add(match)

def search_for_keys():
    """
    在GitHub上搜索潜在的API密钥。
    返回一个包含所有找到的唯一潜在密钥的列表。
    """
    print("\n--- 步骤 1: 在 GitHub 上搜索潜在的 API 密钥 ---")
    os.makedirs(CACHE_DIR, exist_ok=True)
    print(f"GitHub API响应将被缓存到 '{CACHE_DIR}/' 目录中。")
    print("如果脚本中断，请直接重新运行，它将从上次中断的地方继续搜索。")

    token = "*********************************************************************************************"
    
    headers = { "X-GitHub-Api-Version": "2022-11-28" }
    if token:
        headers["Authorization"] = f"Bearer {token}"

    found_strings = set()

    code_headers = headers.copy()
    code_headers["Accept"] = "application/vnd.github.text-match+json"
    code_search_url = urljoin(GITHUB_API_URL, SEARCH_CODE_ENDPOINT)
    repo_headers = headers.copy()
    repo_headers["Accept"] = "application/vnd.github+json"
    repo_search_url = urljoin(GITHUB_API_URL, SEARCH_REPOS_ENDPOINT)
    
    search_tasks = []
    for char in CHAR_SET:
        specific_query = f'"{BASE_QUERY}{char}"'
        search_tasks.append({'type': 'code', 'query': specific_query, 'url': code_search_url, 'headers': code_headers})
        search_tasks.append({'type': 'repo', 'query': f'{specific_query} in:name', 'url': repo_search_url, 'headers': repo_headers})

    with tqdm(total=len(search_tasks), desc="总搜索进度", unit="查询") as main_pbar:
        for task in search_tasks:
            task_type = task['type']
            query = task['query']
            cache_filename = f"cache_{task_type}_{sanitize_filename(query)}.json"
            cache_filepath = os.path.join(CACHE_DIR, cache_filename)
            main_pbar.set_description(f"处理 {query[:20]}...")

            results = []
            if os.path.exists(cache_filepath):
                tqdm.write(f"\n从缓存加载: {cache_filename}")
                with open(cache_filepath, 'r', encoding='utf-8') as f:
                    results = json.load(f)
            else:
                tqdm.write(f"\n通过API获取: {query}")
                results = fetch_all_pages_for_query(task['url'], query, task['headers'], task_type.capitalize())
                if results:
                    with open(cache_filepath, 'w', encoding='utf-8') as f:
                        json.dump(results, f, indent=2)
                    tqdm.write(f"已缓存结果到: {cache_filename}")

            if results:
                extract_strings_from_results(results, found_strings)
            
            main_pbar.update(1)
    
    if found_strings:
        print(f"\n{COLOR_GREEN}搜索完成。所有迭代搜索总共发现 {len(found_strings)} 个唯一的潜在密钥。{COLOR_RESET}")
        return sorted(list(found_strings))
    else:
        print(f"\n{COLOR_YELLOW}搜索完成，但未找到任何符合条件的潜在密钥。{COLOR_RESET}")
        return []

# ==============================================================================
# --- Gemini 密钥验证功能 (源自 check.py) ---
# ==============================================================================

def check_gemini_key(api_key):
    """通过向 Gemini API 发送简单请求来检查 API 密钥的有效性。"""
    if not api_key:
        return False, "Empty key string provided"

    url = f"{API_ENDPOINT_TEMPLATE}?key={api_key}"
    try:
        response = requests.post(url, headers=HEADERS, data=TEST_PAYLOAD, timeout=REQUEST_TIMEOUT)
        if response.status_code == 200:
            try:
                response.json()
                return True, f"{COLOR_GREEN}有效{COLOR_RESET}"
            except json.JSONDecodeError:
                return False, f"{COLOR_YELLOW}警告: 状态 200 但响应不是有效的JSON{COLOR_RESET}"
        elif response.status_code == 400:
            return False, f"{COLOR_RED}密钥无效或请求错误 (HTTP 400){COLOR_RESET}"
        elif response.status_code == 403:
             return False, f"{COLOR_RED}权限被拒绝 (HTTP 403)。检查API启用或限制。{COLOR_RESET}"
        elif response.status_code == 429:
            return False, f"{COLOR_YELLOW}达到速率限制 (HTTP 429)。请尝试增加 DELAY_BETWEEN_REQUESTS。{COLOR_RESET}"
        else:
            return False, f"{COLOR_RED}失败，HTTP状态码 {response.status_code}{COLOR_RESET}"
    except requests.exceptions.Timeout:
        return False, f"{COLOR_YELLOW}请求超时 ({REQUEST_TIMEOUT}s){COLOR_RESET}"
    except requests.exceptions.RequestException as e:
        return False, f"{COLOR_RED}网络或请求错误: {e}{COLOR_RESET}"
    except Exception as e:
        return False, f"{COLOR_RED}发生未知错误: {e}{COLOR_RESET}"

def worker(key_queue, total_keys_to_check, total_keys_found):
    """线程工作函数，从队列中获取并处理密钥"""
    while not key_queue.empty():
        try:
            key, current_index = key_queue.get_nowait()
        except queue.Empty:
            continue

        key_display = f"...{key[-6:]}" if len(key) > 6 else key
        is_valid, status_message = check_gemini_key(key)

        with file_lock:
            with print_lock:
                progress_str = f"[{current_index}/{total_keys_to_check} | 总发现:{total_keys_found}]"
                print(f"{progress_str} 正在检查密钥 ...{key_display}: {status_message}")

            if is_valid:
                with open(VALID_KEYS_FILENAME, 'a') as f_out:
                    f_out.write(key + '\n')

            with open(PROGRESS_CACHE_FILENAME, 'a') as f_cache:
                f_cache.write(key + '\n')
        
        key_queue.task_done()
        time.sleep(DELAY_BETWEEN_REQUESTS)

def check_found_keys(keys_found):
    """
    接收一个密钥列表，使用多线程进行验证，并保存有效密钥。
    """
    print("\n--- 步骤 2: 验证找到的 API 密钥的有效性 ---")

    # 加载已检查过的密钥缓存
    checked_keys = set()
    try:
        if os.path.exists(PROGRESS_CACHE_FILENAME):
            with open(PROGRESS_CACHE_FILENAME, 'r') as f_cache:
                checked_keys = set(line.strip() for line in f_cache)
            print(f"从缓存 '{PROGRESS_CACHE_FILENAME}' 中加载了 {len(checked_keys)} 个已检查的密钥。")
    except Exception as e:
        print(f"{COLOR_RED}加载缓存 '{PROGRESS_CACHE_FILENAME}' 时出错: {e}{COLOR_RESET}")

    # 筛选出未被检查过的新密钥
    keys_to_check = [key for key in keys_found if key not in checked_keys]
    
    if not keys_to_check:
        print(f"{COLOR_GREEN}所有找到的密钥都已经被检查过了。无需新的操作。{COLOR_RESET}")
        return

    total_found = len(keys_found)
    total_to_check = len(keys_to_check)
    print(f"总共发现 {total_found} 个密钥。已缓存/检查: {len(checked_keys)}。本次需要新检查: {total_to_check} 个。")
    print(f"开始使用 {NUM_THREADS} 个线程进行验证 (模型: {MODEL_NAME})...")
    print("-" * 60)

    # 创建并填充队列
    key_queue = queue.Queue()
    for i, key in enumerate(keys_to_check):
        key_queue.put((key, i + 1))

    # 创建并启动线程
    threads = []
    for _ in range(NUM_THREADS):
        thread = threading.Thread(target=worker, args=(key_queue, total_to_check, total_found))
        thread.daemon = True
        thread.start()
        threads.append(thread)

    # 等待队列处理完毕
    key_queue.join()

    # 确保所有线程都已完全退出
    for thread in threads:
        thread.join()

    print("-" * 60)
    print(f"{COLOR_GREEN}密钥验证完成。{COLOR_RESET}")

# ==============================================================================
# --- 主执行流程 ---
# ==============================================================================

def main():
    """主函数，协调搜索和验证流程。"""
    print("--- GitHub Gemini API 密钥扫描与验证工具 ---")
    
    # 步骤 1: 从 GitHub 搜索潜在密钥
    potential_keys = search_for_keys()

    if not potential_keys:
        print("\n未发现任何新的潜在密钥，程序结束。")
        return

    # 步骤 2: 验证找到的密钥
    check_found_keys(potential_keys)

    # 最终总结
    valid_key_count = 0
    if os.path.exists(VALID_KEYS_FILENAME):
         with open(VALID_KEYS_FILENAME, 'r') as f:
             valid_key_count = len([line for line in f if line.strip()])
    
    print("\n--- 全部任务完成 ---")
    print(f"最终在 '{VALID_KEYS_FILENAME}' 文件中保存了 {valid_key_count} 个有效密钥。")


if __name__ == "__main__":
    try:
        import requests
        from tqdm import tqdm
    except ImportError as e:
        missing_module = str(e).split("'")[1]
        print(f"{COLOR_RED}错误: 依赖库 '{missing_module}' 未安装。{COLOR_RESET}")
        print(f"请通过运行以下命令安装: pip install {missing_module}")
        exit()

    main()