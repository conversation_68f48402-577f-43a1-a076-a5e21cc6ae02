{"attachments": [], "date": "Sun, 27 Jul 2025 13:23:00 +0000 (UTC)", "from": "Cursor <<EMAIL>>", "from_is_local": false, "from_mail": "<EMAIL>", "from_name": "<PERSON><PERSON><PERSON>", "html": "<!doctype html><html xmlns=\"http://www.w3.org/1999/xhtml\" xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\"><head><title></title><!--[if !mso]><!--><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><!--<![endif]--><meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\"><meta name=\"viewport\" content=\"width=device-width,initial-scale=1\"><style type=\"text/css\">#outlook a { padding:0; }\n      body { margin:0;padding:0;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%; }\n      table, td { border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt; }\n      img { border:0;height:auto;line-height:100%; outline:none;text-decoration:none;-ms-interpolation-mode:bicubic; }\n      p { display:block;margin:13px 0; }</style><!--[if mso]>\n    <noscript>\n    <xml>\n    <o:OfficeDocumentSettings>\n      <o:AllowPNG/>\n      <o:PixelsPerInch>96</o:PixelsPerInch>\n    </o:OfficeDocumentSettings>\n    </xml>\n    </noscript>\n    <![endif]--><!--[if lte mso 11]>\n    <style type=\"text/css\">\n      .mj-outlook-group-fix { width:100% !important; }\n    </style>\n    <![endif]--><style type=\"text/css\">@media only screen and (min-width:480px) {\n        .mj-column-per-100 { width:100% !important; max-width: 100%; }\n      }</style><style media=\"screen and (min-width:480px)\">.moz-text-html .mj-column-per-100 { width:100% !important; max-width: 100%; }</style><style type=\"text/css\"></style><style type=\"text/css\">@media all and (max-width: 768px) {\n        body {\n          background-color: #FFFFFF !important;\n        }\n      }\n      @media all and (max-width: 768px) {\n        .base-layout-root {\n          background-color: #FFFFFF !important;\n        }\n      }.button-gray table:hover td {\n        border: 1px solid #bbb !important;\n      }\n      .button-gray table:active td, .button-gray table:active a {\n        background-color: #f0f0f0 !important;\n      }\n      .button-accent table:hover td, .button-accent table:hover a {\n        background-color: #2e2e2e !important;\n      }\n      .button-accent table:active td, .button-accent table:active a {\n        background-color: #414141 !important;\n      }.card > table > tbody > tr > td {\n          border-radius: 12px !important;\n          padding: 36px 40px !important;\n        }\n        @media all and (max-width: 1024px) {\n          .card > table > tbody > tr > td {\n            padding: 28px 32px !important;\n          }\n        }\n        @media all and (max-width: 768px) {\n          .card > table > tbody > tr > td {\n            border-radius: 9px !important;\n            padding: 20px 24px !important;\n          }\n        }\n        @media all and (max-width: 520px) {\n          .card > table > tbody > tr > td {\n            padding: 14px 16px !important;\n          }\n        }\n\n        .card-body-with-logo-top > table > tbody > tr > td {\n          border-radius: 12px 12px 0px 0px !important;\n          padding: 32px 32px 0px !important;\n        }\n        @media all and (max-width: 1024px) {\n          .card-body-with-logo-top > table > tbody > tr > td {\n            padding: 24px 24px 0px !important;\n          }\n        }\n        @media all and (max-width: 768px) {\n          .card-body-with-logo-top > table > tbody > tr > td {\n            border-radius: 9px 9px 0px 0px !important;\n            padding: 16px 16px 0px !important;\n          }\n        }\n        @media all and (max-width: 520px) {\n          .card-body-with-logo-top > table > tbody > tr > td {\n            padding: 8px 8px 0px !important;\n          }\n        }\n\n        .card-body-with-logo-bottom > table > tbody > tr > td {\n          border-radius: 0px 0px 12px 12px !important;\n          padding-top: 0px !important;\n        }\n        @media all and (max-width: 768px) {\n          .card-body-with-logo-bottom > table > tbody > tr > td {\n            border-radius: 0px 0px 9px 9px !important;\n          }\n        }.image td {\n        width: 100% !important;\n      }.link:hover {\n        text-decoration-line: underline !important;\n        text-decoration-style: solid !important;\n      }\n      .link-accent:hover {\n        text-decoration-color: #5753c666 !important;\n      }\n      .link-accent.high-contrast {\n        text-decoration-color: #d9d9d9 !important;\n      }\n      .link-gray:hover {\n        text-decoration-color: #d9d9d9 !important;\n      }\n      .link-gray.high-contrast {\n        text-decoration-color: #cecece !important;\n      }\n      .link-green:hover {\n        text-decoration-color: #acdec8 !important;\n      }\n      .link-green.high-contrast {\n        text-decoration-color: #8bceb6 !important;\n      }\n      .link-red:hover {\n        text-decoration-color: #f8bfc8 !important;\n      }\n      .link-red.high-contrast {\n        text-decoration-color: #efacb8 !important;\n      }\n      .link-yellow:hover {\n        text-decoration-color: #f3d768 !important;\n      }\n      .link-yellow.high-contrast {\n        text-decoration-color: #e4c767 !important;\n      }</style><meta name=\"x-apple-disable-message-reformatting\"><meta content=\"light\" name=\"color-scheme\"><meta content=\"light\" name=\"supported-color-schemes\"></head><body style=\"word-spacing:normal;background-color:#FCFCFC;\"><div style=\"display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;\">Your one-time code is 409821. This code expires in 10 minutes. If you didn’t request to sign up for Cursor, you can safely ignore this email.</div><div class=\"base-layout-root\" style=\"background-color:#FCFCFC;\"><table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"width:100%;\"><tbody><tr><td><!--[if mso | IE]><table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"\" role=\"presentation\" style=\"width:674px;\" width=\"674\" ><tr><td style=\"line-height:0px;font-size:0px;mso-line-height-rule:exactly;\"><![endif]--><div style=\"margin:0px auto;max-width:674px;\"><table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"width:100%;\"><tbody><tr><td style=\"direction:ltr;font-size:0px;padding:0;padding-left:16px;padding-right:16px;padding-top:32px;text-align:center;\"><!--[if mso | IE]><table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr><td class=\"card-outlook card-body-with-logo-top-outlook\" style=\"vertical-align:top;width:642px;\" ><![endif]--><div class=\"mj-column-per-100 mj-outlook-group-fix card card-body-with-logo-top\" style=\"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" width=\"100%\" style=\"border-collapse: separate;\"><tbody><tr><td style=\"background-color:#FFFFFF;border:1px solid #e0e0e0;border-bottom:none;border-radius:12px 12px 0px 0px;vertical-align:top;padding:28px 32px 0px;\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" width=\"100%\"><tbody><img alt height=\"80\" src=\"https://og-images.workos.com/api/logo-icon?t=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyYWRpdXMiOiJTbWFsbCIsImxvZ29JY29uRml0IjoiQ29udGFpbiIsImxvZ29TcmMiOiJodHRwczovL3dvcmtvcy5pbWdpeC5uZXQvYXBwLWJyYW5kaW5nL2Vudmlyb25tZW50XzAxR1M2VzNDOTAxTjUwSjRaR0ZCNlYxWjZDLzAxSFozQzY4UzMyREVGS1REQVlLWDg2NERFIiwiaWF0IjoxNzUzNjIyNTgwfQ.W77jL37cE0Iry4g4Mmd6ejABBY-Hc6MdTpstohhVeIU\" width=\"80\" style=\"border:0;display:block;outline:none;text-decoration:none\"><tr><td style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"height:16px;line-height:16px;\">&#8202;</div></td></tr></tbody></table></td></tr></tbody></table></div><!--[if mso | IE]></td></tr></table><![endif]--></td></tr></tbody></table></div><!--[if mso | IE]></td></tr></table><![endif]--></td></tr></tbody></table><table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"width:100%;\"><tbody><tr><td><!--[if mso | IE]><table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"\" role=\"presentation\" style=\"width:674px;\" width=\"674\" ><tr><td style=\"line-height:0px;font-size:0px;mso-line-height-rule:exactly;\"><![endif]--><div style=\"margin:0px auto;max-width:674px;\"><table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" style=\"width:100%;\"><tbody><tr><td style=\"direction:ltr;font-size:0px;padding:0;padding-bottom:24px;padding-left:16px;padding-right:16px;text-align:center;\"><!--[if mso | IE]><table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tr><td class=\"card-outlook card-body-with-logo-bottom-outlook\" style=\"vertical-align:top;width:642px;\" ><![endif]--><div class=\"mj-column-per-100 mj-outlook-group-fix card card-body-with-logo-bottom\" style=\"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" width=\"100%\" style=\"border-collapse: separate;\"><tbody><tr><td style=\"background-color:#FFFFFF;border:1px solid #e0e0e0;border-radius:0px 0px 12px 12px;border-top:none;vertical-align:top;padding:0px 40px 36px;\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" role=\"presentation\" width=\"100%\"><tbody><tr><td align=\"left\" style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:24px;font-weight:600;line-height:30px;text-align:left;color:#202020;\">Sign up for Cursor</div></td></tr><tr><td style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"height:16px;line-height:16px;\">&#8202;</div></td></tr><tr><td align=\"left\" style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:16px;line-height:24px;text-align:left;color:#202020;\">You requested to sign up for Cursor. Your one-time code is:</div></td></tr><tr><td style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"height:24px;line-height:24px;\">&#8202;</div></td></tr><tr><td align=\"left\" style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:28px;font-weight:400;letter-spacing:2px;line-height:30px;text-align:left;color:#202020;\">409821</div></td></tr><tr><td style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"height:32px;line-height:32px;\">&#8202;</div></td></tr><tr><td align=\"center\" style=\"font-size:0px;padding:0;word-break:break-word;\"><p style=\"border-top:solid 1px #d9d9d9;font-size:1px;margin:0px auto;width:100%;\"></p><!--[if mso | IE]><table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"border-top:solid 1px #d9d9d9;font-size:1px;margin:0px auto;width:560px;\" role=\"presentation\" width=\"560px\" ><tr><td style=\"height:0;line-height:0;\"> &nbsp;\n</td></tr></table><![endif]--></td></tr><tr><td style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"height:32px;line-height:32px;\">&#8202;</div></td></tr><tr><td align=\"left\" style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:12px;line-height:18px;text-align:left;color:#646464;\">This code expires in 10 minutes.</div></td></tr><tr><td style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"height:12px;line-height:12px;\">&#8202;</div></td></tr><tr><td align=\"left\" style=\"font-size:0px;padding:0;word-break:break-word;\"><div style=\"font-family:-apple-system, BlinkMacSystemFont, &#x27;Segoe UI&#x27;, Helvetica, Arial, sans-serif;font-size:12px;line-height:18px;text-align:left;color:#646464;\">If you didn’t request to sign up for Cursor, you can safely ignore this email. Someone else might have typed your email address by mistake.</div></td></tr></tbody></table></td></tr></tbody></table></div><!--[if mso | IE]></td></tr></table><![endif]--></td></tr></tbody></table></div><!--[if mso | IE]></td></tr></table><![endif]--></td></tr></tbody></table></div></body></html>", "is_tls": true, "mail_id": 3675044943, "message_id": "<eFF9ATQCR1yeuH2E7ny9ag@geopod-ismtpd-15>", "result": true, "subject": "Sign up for <PERSON><PERSON><PERSON>", "text": "You requested to sign up for Cursor. Your one-time code is:\n\n4 0 9 8 2 1\n\n---\n\nThis code expires in 10 minutes.\n\n\n---\n\nIf you didn’t request to sign up for Cursor, you can\nsafely ignore this email. Someone else might have typed your email\naddress by mistake.", "to": "<EMAIL>"}