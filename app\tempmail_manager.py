import requests
import random
import string
from typing import List, Dict, Optional
from PySide6.QtCore import QObject, Signal, QThread
import urllib3
from .logger import logger

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class TempMailAPI:
    """TempMail.Plus API 客户端"""

    def __init__(self, logger_instance=None, base_domain=None):
        # base_domain 是邮箱域名，base_url 是API服务器地址
        self.base_domain = base_domain or "mailto.plus"  # 邮箱域名
        self.base_url = "https://tempmail.plus"  # API服务器地址
        self.session = requests.Session()

        # 初始化属性
        self.current_email = None
        self.current_pin = ""
        self.logger = logger_instance or logger

        # 禁用SSL验证
        self.session.verify = False

        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Priority': 'u=1, i',
            'Sec-CH-UA': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': f'{self.base_url}/zh/',
            'Origin': f'{self.base_url}'
        })
        
    def generate_random_name(self, length: int = 8) -> str:
        """生成随机邮箱名称"""
        return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))
    
    def get_available_domains(self) -> List[str]:
        """获取可用的邮箱域名"""
        try:
            # 基于网站观察到的域名
            domains = [
                "mailto.plus",
                "fexpost.com", 
                "fexbox.org",
                "mailbox.in.ua",
                "rover.info",
                "chitthi.in",
                "fextemp.com",
                "any.pink",
                "merepost.com"
            ]
            return domains
        except Exception as e:
            self.logger.error(f"获取域名列表失败: {e}")
            return ["mailto.plus"]  # 默认域名
    
    def set_email(self, email: str, pin: str = "") -> Dict:
        """设置要监控的邮箱地址和PIN码

        Args:
            email: 邮箱地址
            pin: PIN码（epin参数）
        """
        try:
            self.current_email = email
            self.current_pin = pin

            # 设置Cookie
            self.session.cookies.set('email', email)

            # 快速测试邮箱是否可用（使用更短的超时时间）
            messages = self.get_messages(email, limit=1)

            pin_info = f" (PIN: {pin})" if pin else ""
            return {
                "success": True,
                "email": email,
                "pin": pin,
                "message": f"邮箱 {email}{pin_info} 设置成功，当前有 {len(messages)} 封邮件"
            }

        except Exception as e:
            self.logger.error(f"设置邮箱失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_messages(self, email: str = None, limit: int = 20, first_id: int = None, timeout: int = 10) -> List[Dict]:
        """获取邮箱中的邮件

        Args:
            email: 邮箱地址
            limit: 获取邮件数量限制
            first_id: 从指定邮件ID开始获取（用于增量更新）
            timeout: 请求超时时间（秒）
        """
        try:
            email = email or self.current_email

            if not email:
                return []

            # 真实的API端点和参数
            params = {
                "email": email,
                "limit": limit,
                "epin": getattr(self, 'current_pin', '')  # 使用当前PIN码
            }

            # 如果指定了first_id，则获取该ID之后的邮件（用于增量更新）
            if first_id:
                params["first_id"] = first_id

            # 设置Cookie
            self.session.cookies.set('email', email)

            response = self.session.get(f"{self.base_url}/api/mails", params=params, timeout=timeout)

            if response.status_code == 200:
                data = response.json()
                if data.get('result'):
                    # 简化的邮件列表日志
                    mail_list = data.get('mail_list', [])
                    count = data.get('count', 0)

                    self.logger.info(f"📬 获取邮件列表: {count} 封邮件")

                    # 转换API返回的邮件格式为统一格式
                    messages = []
                    for i, mail in enumerate(mail_list):
                        message = {
                            'id': mail.get('mail_id'),
                            'from': mail.get('from_mail'),
                            'from_name': mail.get('from_name'),
                            'subject': mail.get('subject'),
                            'time': mail.get('time'),
                            'is_new': mail.get('is_new', False),
                            'attachment_count': mail.get('attachment_count', 0),
                            'first_attachment_name': mail.get('first_attachment_name', '')
                        }
                        messages.append(message)

                    return messages
                else:
                    self.logger.error(f"API返回错误: {data}")
                    return []
            else:
                self.logger.error(f"HTTP错误: {response.status_code}")
                return []

        except Exception as e:
            self.logger.error(f"获取邮件失败: {e}")
            return []

    def get_new_messages(self, email: str = None, last_mail_id: int = None) -> List[Dict]:
        """获取新邮件（增量更新）

        Args:
            email: 邮箱地址
            last_mail_id: 最后一封邮件的ID，获取此ID之后的新邮件
        """
        return self.get_messages(email=email, limit=10, first_id=last_mail_id)

    def get_message_detail(self, email: str = None, mail_id: int = None) -> Dict:
        """获取邮件详情"""
        try:
            email = email or self.current_email

            if not email or not mail_id:
                return {}

            # 真实的邮件详情API端点
            params = {
                "email": email,
                "epin": getattr(self, 'current_pin', '')
            }

            # 设置Cookie
            self.session.cookies.set('email', email)

            # API路径: /api/mails/{mail_id}
            response = self.session.get(f"{self.base_url}/api/mails/{mail_id}", params=params)

            if response.status_code == 200:
                data = response.json()

                # 打印邮件详情API的完整响应
                self.logger.info(f"📧 邮件详情API响应:")

                if data.get('result'):
                    # 打印解析后的邮件详情
                    self.logger.info(f"📧 邮件详情解析:")
                    self.logger.info(f"   🆔 邮件ID: {data.get('mail_id')}")
                    self.logger.info(f"   📨 发件人: {data.get('from_name')} <{data.get('from_mail')}>")
                    self.logger.info(f"   📬 收件人: {data.get('to')}")
                    self.logger.info(f"   📝 主题: {data.get('subject')}")
                    self.logger.info(f"   📅 日期: {data.get('date')}")
                    self.logger.info(f"   🔒 TLS: {data.get('is_tls', False)}")
                    self.logger.info(f"   📎 附件数量: {len(data.get('attachments', []))}")

                    # 打印邮件内容长度
                    text_len = len(data.get('text', ''))
                    html_len = len(data.get('html', ''))
                    self.logger.info(f"   📄 文本内容长度: {text_len} 字符")
                    self.logger.info(f"   🌐 HTML内容长度: {html_len} 字符")

                    # 如果有附件，打印附件信息
                    attachments = data.get('attachments', [])
                    if attachments:
                        self.logger.info(f"   📎 附件列表:")
                        for i, attachment in enumerate(attachments):
                            self.logger.info(f"      [{i+1}] {attachment}")

                    # 直接使用响应数据，字段名已经是正确的
                    return {
                        'id': data.get('mail_id'),
                        'from': data.get('from_mail'),
                        'from_name': data.get('from_name'),
                        'to': data.get('to'),
                        'subject': data.get('subject'),
                        'date': data.get('date'),
                        'message_id': data.get('message_id'),
                        'text_content': data.get('text', ''),
                        'html_content': data.get('html', ''),
                        'attachments': data.get('attachments', []),
                        'is_tls': data.get('is_tls', False),
                        'from_is_local': data.get('from_is_local', False)
                    }
                else:
                    self.logger.error(f"获取邮件详情失败: {data}")
                    return {}
            else:
                self.logger.error(f"HTTP错误: {response.status_code}")
                return {}

        except Exception as e:
            self.logger.error(f"获取邮件详情失败: {e}")
            return {}

    def delete_message(self, email: str = None, mail_id: int = None) -> bool:
        """删除指定邮件"""
        try:
            email = email or self.current_email

            if not email or not mail_id:
                return False

            # 设置Cookie（URL编码格式）
            import urllib.parse
            encoded_email = urllib.parse.quote(email, safe='')
            self.session.cookies.set('email', encoded_email)

            # 设置请求头
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }

            # 请求体数据
            pin = getattr(self, 'current_pin', '')
            data = f"email={email}&first_id={mail_id}&epin={pin}"

            # API调用: DELETE /api/mails/
            response = self.session.delete(
                f"{self.base_url}/api/mails/",
                headers=headers,
                data=data
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('result'):
                    return True
                else:
                    self.logger.error(f"❌ 删除邮件失败: {result}")
                    return False
            else:
                self.logger.error(f"❌ HTTP错误: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 删除邮件异常: {e}")
            return False

    def delete_messages_from_id(self, email: str = None, first_id: int = None) -> bool:
        """批量删除邮件（从指定ID开始的所有邮件）"""
        try:
            email = email or self.current_email

            if not email or not first_id:
                return False

            # 设置Cookie（URL编码格式）
            import urllib.parse
            encoded_email = urllib.parse.quote(email, safe='')
            self.session.cookies.set('email', encoded_email)

            # 设置请求头
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
            }

            # 请求体数据
            pin = getattr(self, 'current_pin', '')
            data = f"email={encoded_email}&first_id={first_id}&epin={pin}"

            # API调用: DELETE /api/mails/
            response = self.session.delete(
                f"{self.base_url}/api/mails/",
                headers=headers,
                data=data
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('result'):
                    self.logger.info(f"✅ 批量删除邮件成功: 从ID {first_id} 开始")
                    return True
                else:
                    self.logger.error(f"❌ 批量删除邮件失败: {result}")
                    return False
            else:
                self.logger.error(f"❌ HTTP错误: {response.status_code}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 批量删除邮件异常: {e}")
            return False

    def clear_all_messages(self, email: str = None) -> bool:
        """清空所有邮件"""
        try:
            email = email or self.current_email

            if not email:
                return False

            # 先重新获取最新的邮件列表
            self.logger.info("📋 重新获取邮件列表...")
            messages = self.get_messages(email, limit=100)

            if not messages:
                self.logger.info("📭 邮箱已经是空的")
                return True

            self.logger.info(f"🗑️ 开始清空邮件，当前有 {len(messages)} 封邮件")

            # 显示邮件ID范围
            oldest_id = min(msg.get('id', 0) for msg in messages)
            newest_id = max(msg.get('id', 0) for msg in messages)
            self.logger.info(f"📊 邮件ID范围: {oldest_id} ~ {newest_id}")

            # 使用最新的邮件ID作为first_id来清空所有邮件
            # 根据API文档，first_id应该是最新邮件的ID，这样会删除所有邮件
            self.logger.info(f"🔄 使用最新邮件ID清空所有邮件: first_id={newest_id}")

            success = self.delete_messages_from_id(email, newest_id)

            if success:
                # 验证是否真的删除了
                import time
                time.sleep(1)  # 等待1秒让删除生效
                remaining_messages = self.get_messages(email, limit=10)
                if not remaining_messages:
                    self.logger.info("✅ 清空成功，所有邮件已删除")
                    return True
                else:
                    self.logger.warning(f"⚠️ 还剩 {len(remaining_messages)} 封邮件，可能需要多次删除")

            return success

        except Exception as e:
            self.logger.error(f"❌ 清空邮件异常: {e}")
            return False

    def delete_mailbox(self, email: str = None) -> bool:
        """删除邮箱（tempmail.plus不支持删除邮箱，只能清空本地状态）"""
        try:
            self.logger.warning("⚠️ tempmail.plus不支持删除邮箱，只清空本地状态")
            self.current_email = None
            self.current_pin = ""
            return True

        except Exception as e:
            self.logger.error(f"清空邮箱状态失败: {e}")
            return False


class EmailMonitorThread(QThread):
    """邮件监控线程"""
    new_message = Signal(dict)  # 新邮件信号

    def __init__(self, api: TempMailAPI, email: str, logger_instance=None):
        super().__init__()
        self.api = api
        self.email = email
        self.running = True
        self.last_mail_id = None
        self.logger = logger_instance or logger

    def run(self):
        """运行邮件监控"""
        self.logger.info(f"📡 开始监控邮箱: {self.email}")

        # 步骤1: 首次获取所有邮件列表，确定最新邮件ID
        try:
            self.logger.debug("📋 获取邮件列表...")
            initial_messages = self.api.get_messages(self.email, limit=20)

            if initial_messages:
                # 从邮件列表中找最大ID作为监控基准
                self.last_mail_id = max(msg.get('id', 0) for msg in initial_messages)

                self.logger.info(f"📊 邮件列表获取成功:")
                self.logger.debug(f"   - 邮件数量: {len(initial_messages)}")
                self.logger.debug(f"   - 最新邮件ID: {self.last_mail_id}")
                self.logger.debug(f"   - 最新邮件: {initial_messages[0].get('subject', '无主题')}")
            else:
                self.logger.info("📭 邮箱暂无邮件")
                self.logger.debug("🔍 将使用 first_id=0 监控新邮件")
                self.last_mail_id = 0  # 设置为0，符合官方API用法

        except Exception as e:
            self.logger.error(f"❌ 获取邮件列表失败: {e}")
            self.logger.debug("🔍 将监控所有新邮件（不使用first_id参数）")
            self.last_mail_id = None

        self.logger.info(f"🔍 开始监控新邮件 (从ID {self.last_mail_id} 开始)...")

        # 步骤2: 循环监控新邮件
        while self.running:
            try:
                # 始终使用 first_id 参数检查新邮件（包括 first_id=0 的情况）
                # 官方API支持 first_id=0 来监控空邮箱
                new_messages = self.api.get_new_messages(self.email, self.last_mail_id)

                if new_messages:
                    self.logger.info(f"🆕 发现 {len(new_messages)} 封新邮件!")

                    # 发送新邮件信号并更新最新ID
                    for message in new_messages:
                        self.logger.debug(f"   📧 {message.get('subject')} (ID: {message.get('id')})")
                        self.new_message.emit(message)

                        # 更新最后邮件ID
                        mail_id = message.get('id', 0)
                        if mail_id > self.last_mail_id:
                            self.last_mail_id = mail_id

                    self.logger.debug(f"🔄 更新最新邮件ID: {self.last_mail_id}")
                else:
                    # 静默检查，不输出"暂无新邮件"
                    pass

                # 每10秒检查一次新邮件
                self.msleep(10000)

            except Exception as e:
                self.logger.error(f"❌ 邮件监控错误: {e}")
                self.msleep(10000)  # 出错时等待10秒

    def stop(self):
        """停止监控"""
        self.running = False


class TempMailManager(QObject):
    """临时邮箱监控管理器"""

    # 信号定义
    email_set = Signal(dict)  # 邮箱设置成功
    new_message_received = Signal(dict)  # 收到新邮件
    monitoring_started = Signal(str)  # 开始监控
    monitoring_stopped = Signal()  # 停止监控
    log_message = Signal(str)  # 日志消息

    def __init__(self, base_domain=None):
        super().__init__()
        self.api = TempMailAPI(logger, base_domain)
        self.monitor_thread = None
        self.current_email = None
        self.last_mail_id = None  # 记录最后一封邮件的ID，用于增量更新

    def update_domain(self, domain: str):
        """更新API域名"""
        try:
            # 停止当前监控
            if self.monitor_thread and self.monitor_thread.running:
                self.stop_monitoring()

            # 创建新的API客户端
            self.api = TempMailAPI(logger, domain)
            logger.info(f"🔄 已更新API域名为: {domain}")

        except Exception as e:
            logger.error(f"❌ 更新域名失败: {e}")
            raise
        
    def set_email(self, email: str, pin: str = "") -> Dict:
        """设置要监控的邮箱地址"""
        result = self.api.set_email(email, pin)
        if result.get("success"):
            self.current_email = email
            self.current_pin = pin  # 保存PIN码

            # 先获取当前邮件列表，初始化最新邮件ID
            try:
                messages = self.get_messages()
                if messages:
                    # 从邮件列表中找最大ID作为监控基准
                    self.last_mail_id = max(msg.get('id', 0) for msg in messages)
                    result["message"] = f"邮箱设置成功，当前有 {len(messages)} 封邮件，监控ID: {self.last_mail_id}"
                else:
                    self.last_mail_id = 0  # 0表示邮箱为空，符合官方API
                    result["message"] = f"邮箱设置成功，当前暂无邮件，将监控所有新邮件"
            except Exception as e:
                logger.error(f"获取邮件列表失败: {e}")
                self.last_mail_id = None

            self.email_set.emit(result)

            # 开始监控邮件
            self.start_monitoring(email)

        return result
    
    def start_monitoring(self, email: str):
        """开始监控邮件"""
        if self.monitor_thread and self.monitor_thread.isRunning():
            self.monitor_thread.stop()
            self.monitor_thread.wait()

        self.monitor_thread = EmailMonitorThread(self.api, email, logger)
        self.monitor_thread.new_message.connect(self.new_message_received.emit)
        self.monitor_thread.start()

        self.monitoring_started.emit(email)
        logger.info(f"📡 开始监控邮箱: {email}")
    
    def stop_monitoring(self):
        """停止监控邮件"""
        if self.monitor_thread and self.monitor_thread.isRunning():
            self.monitor_thread.stop()
            self.monitor_thread.wait()

        self.monitoring_stopped.emit()
        logger.info("⏹️ 邮件监控已停止")
    
    def get_messages(self) -> List[Dict]:
        """获取当前邮箱的邮件"""
        if not self.current_email:
            return []

        messages = self.api.get_messages(self.current_email)

        # 更新最后邮件ID
        if messages:
            self.last_mail_id = max(msg.get('id', 0) for msg in messages)
        else:
            # 如果没有邮件，设置为0（符合官方API用法）
            self.last_mail_id = 0

        return messages

    def get_new_messages(self) -> List[Dict]:
        """获取新邮件（增量更新）"""
        if not self.current_email:
            return []

        # 始终使用 first_id 参数进行增量更新
        # 官方API支持 first_id=0 来监控空邮箱
        new_messages = self.api.get_new_messages(
            self.current_email,
            self.last_mail_id
        )

        return new_messages

    def refresh_after_clear(self):
        """清空邮件后刷新状态"""
        if not self.current_email:
            return

        logger.info("🔄 邮件已清空，重置监控状态...")

        # 重置监控ID
        self.last_mail_id = None

        # 重新获取邮件列表（应该为空）
        messages = self.get_messages()

        if not messages:
            logger.info("✅ 确认邮箱已清空，开始监控新邮件")
        else:
            logger.warning(f"⚠️ 邮箱仍有 {len(messages)} 封邮件")
            # 更新最新ID
            self.last_mail_id = max(msg.get('id', 0) for msg in messages)

    def clear_current_email(self):
        """清除当前邮箱设置"""
        self.stop_monitoring()
        self.current_email = None
        self.last_mail_id = None
        logger.info("🗑️ 已清除邮箱设置")
    
    def get_available_domains(self) -> List[str]:
        """获取可用域名列表"""
        return self.api.get_available_domains()
    
    def get_current_email(self) -> Optional[str]:
        """获取当前邮箱地址"""
        return self.current_email

    def is_monitoring(self) -> bool:
        """检查是否正在监控"""
        return self.monitor_thread and self.monitor_thread.isRunning()
